﻿using System.ComponentModel.DataAnnotations;

namespace WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormNoneEG;
public class BankFormNoneEGDtoValidator : AbstractValidator<BankFormNoneEGDto>
{
    public BankFormNoneEGDtoValidator()
    {
        RuleFor(x => x.AccountHolderName)
            .NotEmpty().WithMessage("AccountHoldeNameRequired")
            .Matches(@"^[A-Za-z ]*$").WithMessage("BankNote");

        RuleFor(x => x.BankName)
            .NotEmpty().WithMessage("BankNameRequired")
            .Matches("^[A-Za-z0-9\\s]*$").WithMessage("BankNote");

        RuleFor(x => x.AccountHolderResidentialAddress)
            .NotEmpty().WithMessage("AccountHolderResidentialAddressRequired")
            .Matches("^[A-Za-z0-9\\s]*$").WithMessage("BankNote");

        RuleFor(x => x.BankBranchName)
            .NotEmpty().WithMessage("BankBranchNameRequired")
            .Matches("^[A-Za-z0-9\\s]*$").WithMessage("BankNote");

        RuleFor(x => x.BankAddress)
            .NotEmpty().WithMessage("BankAddressRequired");

        RuleFor(x => x.AccountNo)
            .NotEmpty().WithMessage("AccountNumberRequired")
            .Matches("^[A-Za-z0-9]*$").WithMessage("BankNote")
            .MaximumLength(30).WithMessage("MaxAccountNumber");

        RuleFor(x => x.SWIFTCode)
            .MaximumLength(15).WithMessage("SwiftCodeMaxlength")
            .Matches("^[A-Za-z0-9]*$").WithMessage("BankNote")
            .When(x => !string.IsNullOrEmpty(x.SWIFTCode));

        RuleFor(x => x.IBAN)
            .NotEmpty().WithMessage("IBANRequired")
            .MinimumLength(5).WithMessage("IBANMinlength")
            .MaximumLength(34).WithMessage("IBANMaxlength")
            .Matches("^[A-Za-z0-9]*$").WithMessage("BankNote");
    }
}
