{"x-generator": "NSwag v14.1.0.0 (NJsonSchema v11.0.2.0 (Newtonsoft.Json v13.0.0.0))", "openapi": "3.0.0", "info": {"title": "WaffarX API", "version": "1.0.0"}, "paths": {"/": {"get": {"operationId": "Get", "responses": {"200": {"description": ""}}}}, "/api/Account/Signup": {"post": {"tags": ["Account"], "operationId": "Account_Signup", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SignupModel"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/SignupWithMobile": {"post": {"tags": ["Account"], "operationId": "Account_SignupWithMobile", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MobileSignupDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/SendSignupVerification": {"post": {"tags": ["Account"], "operationId": "Account_SignupVerification", "requestBody": {"x-name": "signupCodeDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendSignupCodeDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/Login": {"post": {"tags": ["Account"], "operationId": "Account_Login", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/LoginDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/SocialLogin": {"post": {"tags": ["Account"], "operationId": "Account_SocialLogin", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SocialLoginDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/SendVerification": {"post": {"tags": ["Account"], "operationId": "Account_SendVerification", "requestBody": {"x-name": "verificationCodeDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendVerificationCodeDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/ReSendVerification": {"post": {"tags": ["Account"], "operationId": "Account_ReSendVerification", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/VerifyMobile": {"post": {"tags": ["Account"], "operationId": "Account_VerifyMobileNumber", "requestBody": {"x-name": "verifyMobileDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyMobileNumberDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/ForgetPassword": {"post": {"tags": ["Account"], "operationId": "Account_ForgetPasswordByEmail", "requestBody": {"x-name": "emailDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendEmailDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/CheckPassCode": {"post": {"tags": ["Account"], "operationId": "Account_CheckPassCode", "requestBody": {"x-name": "passCodeDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CheckPassCodeDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/SendForgetPasswordCode": {"post": {"tags": ["Account"], "operationId": "Account_SendForgetPassword", "requestBody": {"x-name": "forgetPasswordCodeDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendForgetPasswordCodeDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/ResendSendForgetPasswordCode": {"post": {"tags": ["Account"], "operationId": "Account_ResendSendForgetPassword", "requestBody": {"x-name": "forgetPasswordCodeDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendForgetPasswordCodeDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/VerifyForgetPasswordCode": {"post": {"tags": ["Account"], "operationId": "Account_VerifyForgetPasswordCode", "requestBody": {"x-name": "verifyForgetPasswordCodeDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/VerifyForgetPasswordCodeDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/ResetUserPasswordEmail": {"post": {"tags": ["Account"], "operationId": "Account_ResetUserPasswordByEmail", "requestBody": {"x-name": "resetPasswordEmailDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordEmailDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/ResetUserPasswordMobile": {"post": {"tags": ["Account"], "operationId": "Account_ResetUserPasswordByMobile", "requestBody": {"x-name": "resetPasswordMobileDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordMobileDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Account/GetUserData": {"post": {"tags": ["Account"], "operationId": "Account_UserData", "requestBody": {"x-name": "userDataByGuidDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserDataByGuidDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Cashback/GetCashbackById": {"post": {"tags": ["Cashback"], "operationId": "Cashback_GetCashbackById", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetCashbackDTO"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Cashback/UserCashbacks": {"get": {"tags": ["Cashback"], "operationId": "Cashback_GetUserCashbacks", "parameters": [{"name": "pageNo", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 1}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Cashback/UserCashbackSummary": {"get": {"tags": ["Cashback"], "operationId": "Cashback_Cashback<PERSON><PERSON><PERSON><PERSON>", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Cashback/UserCashbackEligibilty": {"get": {"tags": ["Cashback"], "operationId": "Cashback_CashbackEligibilty", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Cashout/GetUserCashout": {"get": {"tags": ["Cashout"], "operationId": "Cashout_GetUserCashout", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Category/GetAllOnlineStoresByCategoryTitle": {"post": {"tags": ["Category"], "operationId": "Category_GetAllOnlineStoresByCategoryTitle", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoresByCategoryFiltersDTO"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Category/GetAllInstoreByCategoryTitle": {"post": {"tags": ["Category"], "operationId": "Category_GetAllInstoreByCategoryTitle", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoresByCategoryFiltersDTO"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Category/GetCategory": {"get": {"tags": ["Category"], "operationId": "Category_GetCategory", "parameters": [{"name": "typeId", "in": "query", "schema": {"type": "integer", "format": "int32"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Deal/GetDealPages": {"get": {"tags": ["Deal"], "operationId": "Deal_GetDealPages", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Deal/DealTopStores": {"post": {"tags": ["Deal"], "operationId": "Deal_DealTopStores", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetByDealPageDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Deal/GetDealsPageBanner": {"post": {"tags": ["Deal"], "operationId": "Deal_GetDealsPageBanner", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetByDealPageDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Deal/GetHotDealsByDealsPage": {"post": {"tags": ["Deal"], "operationId": "Deal_GetHotDealsByDealsPage", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetByDealPageDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/FAQ/GetFAQsCategory": {"get": {"tags": ["FAQ"], "operationId": "FAQ_GetFAQsCategory", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/FAQ/GetFAQsByCategory": {"post": {"tags": ["FAQ"], "operationId": "FAQ_GetFAQsByCategory", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FAQCategoryRequestDTO"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/FAQ/TermsAndPrivacy": {"get": {"tags": ["FAQ"], "operationId": "FAQ_GetTermsAndPrivacy", "parameters": [{"name": "sourceId", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/FAQ/TermAndCondition": {"get": {"tags": ["FAQ"], "operationId": "FAQ_GetTermAndCondition", "parameters": [{"name": "sourceId", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/FAQ/PrivacyPolicy": {"get": {"tags": ["FAQ"], "operationId": "FAQ_GetPrivacyPolicy", "parameters": [{"name": "sourceId", "in": "query", "schema": {"type": "integer", "format": "int32", "nullable": true}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/FAQ/About": {"get": {"tags": ["FAQ"], "operationId": "FAQ_GetAbout", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/FAQ/ContactUsTypes": {"get": {"tags": ["FAQ"], "operationId": "FAQ_GetContactUsTypes", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/FAQ/ExitClickStores": {"get": {"tags": ["FAQ"], "operationId": "FAQ_GetOnlineExitClicks", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/FAQ/GetInstoreLinks": {"get": {"tags": ["FAQ"], "operationId": "FAQ_GetInstoreLinks", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/FAQ/GetContactUsPaymentMethods": {"get": {"tags": ["FAQ"], "operationId": "FAQ_GetContactUsPaymentMethod", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/FAQ/GetLinkByStore": {"get": {"tags": ["FAQ"], "operationId": "FAQ_GeInstoreLinktByStore", "parameters": [{"name": "storeId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/FAQ/ExitClickByStore": {"get": {"tags": ["FAQ"], "operationId": "FAQ_GetExitClickByStore", "parameters": [{"name": "storeId", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/FAQ/InsertUserTicket": {"post": {"tags": ["FAQ"], "operationId": "FAQ_InsertUserTicket", "requestBody": {"x-name": "requestDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertTicketRequestDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/FAQ/InsertCashbackUserTicket": {"post": {"tags": ["FAQ"], "operationId": "FAQ_InsertCashbackUserTicket", "requestBody": {"x-name": "requestDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InsertCashbackTicketDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/General/TopStoresList": {"get": {"tags": ["General"], "operationId": "General_GetTopStoresList", "parameters": [{"name": "PageNo", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 1}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 12}, "x-position": 2}, {"name": "CountryId", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 300}, "x-position": 3}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/General/TotalExitClickAndTotalCashback": {"get": {"tags": ["General"], "operationId": "General_GetTotalExitClickAndTotalCashback", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Home/GetBanners": {"get": {"tags": ["Home"], "operationId": "Home_GetBanners", "parameters": [{"name": "campaignId", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 0}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Home/GetUserStageAndBanners": {"get": {"tags": ["Home"], "operationId": "Home_GetUserStageAndBanners", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Home/BannerRows": {"get": {"tags": ["Home"], "operationId": "Home_BannerRows", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Home/TopStores": {"get": {"tags": ["Home"], "operationId": "Home_GetTopStores", "parameters": [{"name": "campaignId", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 0}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Home/DoubleCashback": {"get": {"tags": ["Home"], "operationId": "Home_DoubleCashback", "parameters": [{"name": "campaignId", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 0}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Home/AllNewlyAddedStores": {"get": {"tags": ["Home"], "operationId": "Home_AllNewlyAddedStores", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Home/HomeInStoreStores": {"get": {"tags": ["Home"], "operationId": "Home_HomeInStoreStores", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Home/HotDeals": {"get": {"tags": ["Home"], "operationId": "Home_HotDeals", "parameters": [{"name": "campaignId", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 0}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Home/DeleteKey": {"get": {"tags": ["Home"], "operationId": "Home_DeleteKey", "parameters": [{"name": "key", "in": "query", "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Home/GetKeys": {"get": {"tags": ["Home"], "operationId": "Home_GetKeys", "parameters": [{"name": "check", "in": "query", "schema": {"type": "boolean"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Home/SetKey": {"get": {"tags": ["Home"], "operationId": "Home_SetKey", "parameters": [{"name": "key", "in": "query", "schema": {"type": "string"}, "x-position": 1}, {"name": "value", "in": "query", "schema": {"type": "string"}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Home/CheckLocalization": {"get": {"tags": ["Home"], "operationId": "Home_CheckLocalization", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Instore/DisableInStoreCard": {"post": {"tags": ["Instore"], "operationId": "Instore_DisableInStoreCard", "requestBody": {"x-name": "requestDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DisableStoreCardRequestDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Instore/InStoreCards": {"get": {"tags": ["Instore"], "operationId": "Instore_InStoreCards", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Instore/InStoreLinksHistory": {"get": {"tags": ["Instore"], "operationId": "Instore_InStoreLinksHistory", "parameters": [{"name": "pageNo", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 1}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Instore/InStoreLinksOngoing": {"get": {"tags": ["Instore"], "operationId": "Instore_InStoreLinksOngoing", "parameters": [{"name": "pageNo", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 1}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Instore/GetInstoreStore": {"post": {"tags": ["Instore"], "operationId": "Instore_GetInstoreStore", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetByAdvertiserGuidDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Instore/LinkUserToInstoreStore": {"post": {"tags": ["Instore"], "operationId": "Instore_InsertInstoreCardLink", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetByAdvertiserGuidDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Instore/GetUserInStoreLink": {"post": {"tags": ["Instore"], "operationId": "Instore_GetUserInStoreLink", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/InstoreLinkDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Instore/GetAllNewlyAddedInstore": {"post": {"tags": ["Instore"], "operationId": "Instore_GetAllNewlyAddedInstore", "requestBody": {"x-name": "categoryFiltersDTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoresByCategoryFiltersDTO"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Instore/GetAllInStoreHome": {"post": {"tags": ["Instore"], "operationId": "Instore_GetAllInStoreHome", "requestBody": {"x-name": "categoryFiltersDTO", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/StoresByCategoryFiltersDTO"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/GetBanks": {"get": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_GetBanks", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/GetBankBranches": {"post": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_GetBankBranches", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetBankBranchDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/GetCibUserMethod": {"get": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_GetCibUserMethod", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/GetPaymentMethods": {"post": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_GetPaymentMethods", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPaymentMethodDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/GetWallets": {"get": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_GetWallets", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/GetAgcodStoresByCountry": {"post": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_GetAgcodStoresByCountry", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetPaymentMethodDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/GetCashoutCountries": {"get": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_GetCashoutCountries", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/BankFormEGUpdate": {"post": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_BankFormEGUpdate", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BankFormEGDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/FawryUpdate": {"post": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_FawryUpdate", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/FawryDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/WalletUpdate": {"post": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_WalletUpdate", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/WalletDataDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/BankFormNoneEGUpdate": {"post": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_BankFormNoneEGUpdate", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/BankFormNoneEGDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/MoroccoBankUpdate": {"post": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_MoroccoBankUpdate", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/MorocoBankDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/ItalyBankUpdate": {"post": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_ItalyBankUpdate", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/ItalyBankDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/KSABankUpdate": {"post": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_KSABankUpdate", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/KSABankDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/CreditCardUpdate": {"post": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_CreditCardUpdate", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CreditCardFormEGDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/AmazonGiftCardUpdate": {"post": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_AmazonGiftCardUpdate", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AmazonGiftCardPaymentDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/CIBBankTransferUpdate": {"post": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_CIBBankTransferUpdate", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CIBBankTransferDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/PaymentSetting/SendCashoutMobileCode": {"post": {"tags": ["PaymentSetting"], "operationId": "PaymentSetting_SendCashoutCode", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/SendCashoutCodeDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Resources/cache": {"get": {"tags": ["Resources"], "operationId": "Resources_Cache", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Resources/getresource": {"get": {"tags": ["Resources"], "operationId": "Resources_GetResource", "parameters": [{"name": "key", "in": "query", "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Resources/Resources": {"get": {"tags": ["Resources"], "operationId": "Resources_GetResources", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Stores/TopStores": {"get": {"tags": ["Stores"], "operationId": "Stores_GetTopStores", "parameters": [{"name": "pageNo", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 1}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Stores/DoubleCashback": {"get": {"tags": ["Stores"], "operationId": "Stores_DoubleCashback", "parameters": [{"name": "campaignId", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 0}, "x-position": 1}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 12}, "x-position": 3}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Stores/AllNewLyAddedStores": {"get": {"tags": ["Stores"], "operationId": "Stores_AllNewLyAddedStores", "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 1}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 12}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Stores/StoreDetails": {"post": {"tags": ["Stores"], "operationId": "Stores_StoreDetails", "requestBody": {"x-name": "advertiserDetailsDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetAdvertiserDetailsDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Stores/AdvertiserRelatedStores": {"post": {"tags": ["Stores"], "operationId": "Stores_AdvertiserRelatedStores", "requestBody": {"x-name": "advertiserGuidDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetByAdvertiserGuidDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Stores/GetCouponsDataByAdvertiserId": {"post": {"tags": ["Stores"], "operationId": "Stores_GetCouponsDataByAdvertiserId", "requestBody": {"x-name": "advertiserGuidDto", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/GetByAdvertiserGuidDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Stores/HotDeals": {"get": {"tags": ["Stores"], "operationId": "Stores_HotDeals", "parameters": [{"name": "campaignId", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 0}, "x-position": 1}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 2}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 8}, "x-position": 3}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Stores/FavouriteStores": {"get": {"tags": ["Stores"], "operationId": "Stores_FavouriteStores", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Stores/StoresWithCopouns": {"get": {"tags": ["Stores"], "operationId": "Stores_GetStoresWithCopouns", "parameters": [{"name": "pageNo", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 1}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Stores/GetAllStoresList": {"get": {"tags": ["Stores"], "operationId": "Stores_GetAllStoresList", "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 1}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 12}, "x-position": 2}, {"name": "sortby", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}, "x-position": 3}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Stores/AllStoresSearch": {"post": {"tags": ["Stores"], "operationId": "Stores_AllStoresSearch", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/AllStoresSearchDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Users/<USER>": {"post": {"tags": ["Users"], "operationId": "Users_DeleteUserAccount", "requestBody": {"x-name": "model", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DeleteAccountDto"}}}, "required": true, "x-position": 1}, "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Extension/ExtensionStores": {"get": {"tags": ["Extension"], "operationId": "Extension_GetExtensionStores", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Extension/GetUserBalance": {"get": {"tags": ["Extension"], "operationId": "Extension_GetUserBalance", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Extension/GetRecentVisitedStores": {"get": {"tags": ["Extension"], "operationId": "Extension_GetRecentVisitedStores", "parameters": [{"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 4}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Extension/TopStores": {"get": {"tags": ["Extension"], "operationId": "Extension_GetTopStores", "parameters": [{"name": "pageNo", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 0}, "x-position": 1}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 50}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Extension/GetUserEmail": {"get": {"tags": ["Extension"], "operationId": "Extension_GetUserEmail", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Extension/GetValidCouponsCodesByAdvertiserId": {"get": {"tags": ["Extension"], "operationId": "Extension_GetValidCouponsCodesByAdvertiserId", "parameters": [{"name": "id", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Extension/GetCouponsCodesDataByAdvertiserId": {"get": {"tags": ["Extension"], "operationId": "Extension_GetCouponsCodesDataByAdvertiserId", "parameters": [{"name": "id", "in": "query", "schema": {"type": "string", "format": "guid"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Extension/SearchStoreExtension": {"post": {"tags": ["Extension"], "operationId": "Extension_SearchStoreExtension", "parameters": [{"name": "filter", "in": "query", "schema": {"type": "string"}, "x-position": 1}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Extension/HotDeals": {"get": {"tags": ["Extension"], "operationId": "Extension_HomeHotDeals", "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}, "/api/Extension/StoreDetails": {"get": {"tags": ["Extension"], "operationId": "Extension_StoreDetails", "parameters": [{"name": "Title", "in": "query", "schema": {"type": "string"}, "x-position": 1}, {"name": "AdvertiserId", "in": "query", "schema": {"type": "string", "format": "guid", "nullable": true}, "x-position": 2}], "responses": {"200": {"description": "", "content": {"application/octet-stream": {"schema": {"type": "string", "format": "binary"}}}}}}}}, "components": {"schemas": {"SignupModel": {"type": "object", "additionalProperties": false, "properties": {"fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "referalCode": {"type": "string", "nullable": true}, "sourceId": {"type": "integer", "format": "int32"}, "offerCode": {"type": "string", "nullable": true}, "accessToken": {"type": "string", "nullable": true}}}, "MobileSignupDto": {"type": "object", "additionalProperties": false, "properties": {"fullName": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "referalCode": {"type": "string", "nullable": true}, "sourceId": {"type": "integer", "format": "int32"}, "offerCode": {"type": "string", "nullable": true}, "mobileNumber": {"type": "string", "nullable": true}, "otpCode": {"type": "string", "nullable": true}, "accessToken": {"type": "string", "nullable": true}}}, "SendSignupCodeDto": {"type": "object", "additionalProperties": false, "properties": {"mobileNumber": {"type": "string", "nullable": true}, "countryCode": {"type": "integer", "format": "int32"}}}, "LoginDto": {"type": "object", "additionalProperties": false, "properties": {"loginType": {"type": "integer", "format": "int32"}, "email": {"type": "string", "nullable": true}, "mobileNumber": {"type": "string", "nullable": true}, "password": {"type": "string", "nullable": true}, "accessToken": {"type": "string", "nullable": true}}}, "SocialLoginDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "string", "nullable": true}, "fullName": {"type": "string", "nullable": true}, "email": {"type": "string", "nullable": true}, "referalCode": {"type": "string", "nullable": true}, "sourceId": {"type": "integer", "format": "int32"}, "typeId": {"type": "integer", "format": "int32"}, "offerCode": {"type": "string", "nullable": true}}}, "SendVerificationCodeDto": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string", "nullable": true}, "mobileNumber": {"type": "string", "nullable": true}, "countryCode": {"type": "integer", "format": "int32"}}}, "VerifyMobileNumberDto": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string", "nullable": true}, "verificationCode": {"type": "string", "nullable": true}}}, "SendEmailDto": {"type": "object", "additionalProperties": false, "properties": {"email": {"type": "string", "nullable": true}}}, "CheckPassCodeDto": {"type": "object", "additionalProperties": false, "properties": {"passCode": {"type": "string", "nullable": true}, "pId": {"type": "integer", "format": "int64"}}}, "SendForgetPasswordCodeDto": {"type": "object", "additionalProperties": false, "properties": {"mobileNumber": {"type": "string", "nullable": true}, "countryCode": {"type": "integer", "format": "int32"}}}, "VerifyForgetPasswordCodeDto": {"type": "object", "additionalProperties": false, "properties": {"mobileNumber": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}}}, "ResetPasswordEmailDto": {"allOf": [{"$ref": "#/components/schemas/CheckPassCodeDto"}, {"type": "object", "additionalProperties": false, "properties": {"password": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}}}]}, "ResetPasswordMobileDto": {"type": "object", "additionalProperties": false, "properties": {"password": {"type": "string", "nullable": true}, "confirmPassword": {"type": "string", "nullable": true}, "code": {"type": "string", "nullable": true}}}, "UserDataByGuidDto": {"type": "object", "additionalProperties": false, "properties": {"userId": {"type": "string", "nullable": true}}}, "GetCashbackDTO": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int32"}}}, "StoresByCategoryFiltersDTO": {"type": "object", "additionalProperties": false, "properties": {"sortBy": {"type": "integer", "format": "int32"}, "pageNo": {"type": "integer", "format": "int32"}, "count": {"type": "integer", "format": "int32"}, "countryId": {"type": "integer", "format": "int32"}, "categoryTitle": {"type": "string", "nullable": true}, "sourceId": {"type": "integer", "format": "int32"}}}, "GetByDealPageDto": {"type": "object", "additionalProperties": false, "properties": {"dealPageTitle": {"type": "string", "nullable": true}}}, "FAQCategoryRequestDTO": {"type": "object", "additionalProperties": false, "properties": {"categoryId": {"type": "integer", "format": "int32", "nullable": true}, "isfeatured": {"type": "boolean", "nullable": true}}}, "InsertTicketRequestDto": {"type": "object", "additionalProperties": false, "properties": {"textMessage": {"type": "string", "nullable": true}, "typeId": {"type": "integer", "format": "int32"}, "paymentMethodId": {"type": "integer", "format": "int32"}, "referFriendIssuesId": {"type": "integer", "format": "int32"}, "referFriendEmailId": {"type": "string", "nullable": true}}}, "InsertCashbackTicketDto": {"type": "object", "additionalProperties": false, "properties": {"typeId": {"type": "integer", "format": "int32"}, "orderNumber": {"type": "string", "nullable": true}, "textMessage": {"type": "string", "nullable": true}, "shoppingTrip": {"type": "integer", "format": "int64"}, "orderSubtotal": {"type": "number", "format": "double"}}}, "DisableStoreCardRequestDto": {"type": "object", "additionalProperties": false, "properties": {"cardId": {"type": "integer", "format": "int32"}}}, "GetByAdvertiserGuidDto": {"allOf": [{"$ref": "#/components/schemas/AdvertiserBaseDto"}, {"type": "object", "additionalProperties": false}]}, "AdvertiserBaseDto": {"type": "object", "additionalProperties": false, "properties": {"advertiserId": {"type": "string", "format": "guid", "nullable": true}}}, "InstoreLinkDto": {"type": "object", "additionalProperties": false, "properties": {"id": {"type": "integer", "format": "int64"}}}, "GetBankBranchDto": {"type": "object", "additionalProperties": false, "properties": {"bankId": {"type": "integer", "format": "int32"}}}, "GetPaymentMethodDto": {"type": "object", "additionalProperties": false, "properties": {"countryId": {"type": "integer", "format": "int32"}}}, "BankFormEGDto": {"allOf": [{"$ref": "#/components/schemas/BasePaymentMethodDto"}, {"type": "object", "additionalProperties": false, "properties": {"accountHolderName": {"type": "string", "nullable": true}, "bankId": {"type": "integer", "format": "int32"}, "branchId": {"type": "integer", "format": "int32"}, "accountNumber": {"type": "string", "nullable": true}, "swiftCode": {"type": "string", "nullable": true}}}]}, "BasePaymentMethodDto": {"type": "object", "x-abstract": true, "additionalProperties": false, "properties": {"userID": {"type": "integer", "format": "int32", "nullable": true}}}, "FawryDto": {"allOf": [{"$ref": "#/components/schemas/BasePaymentMethodDto"}, {"type": "object", "additionalProperties": false, "properties": {"countryID": {"type": "integer", "format": "int32"}, "fawryName": {"type": "string", "nullable": true}, "fawryNumber": {"type": "string", "nullable": true}}}]}, "WalletDataDto": {"allOf": [{"$ref": "#/components/schemas/BasePaymentMethodDto"}, {"type": "object", "additionalProperties": false, "properties": {"walletID": {"type": "integer", "format": "int32", "nullable": true}, "walletName": {"type": "string", "nullable": true}, "walletNumber": {"type": "string", "nullable": true}}}]}, "BankFormNoneEGDto": {"allOf": [{"$ref": "#/components/schemas/BasePaymentMethodDto"}, {"type": "object", "additionalProperties": false, "properties": {"accountHolderName": {"type": "string", "nullable": true}, "bankName": {"type": "string", "nullable": true}, "accountHolderResidentialAddress": {"type": "string", "nullable": true}, "bankBranchName": {"type": "string", "nullable": true}, "bankAddress": {"type": "string", "nullable": true}, "accountNo": {"type": "string", "nullable": true}, "swiftCode": {"type": "string", "nullable": true}, "iban": {"type": "string", "nullable": true}, "countryID": {"type": "integer", "format": "int32"}}}]}, "MorocoBankDto": {"allOf": [{"$ref": "#/components/schemas/BasePaymentMethodDto"}, {"type": "object", "additionalProperties": false, "properties": {"firstName": {"type": "string", "nullable": true}, "middleName": {"type": "string", "nullable": true}, "lastName": {"type": "string", "nullable": true}, "dob": {"type": "string", "format": "date-time", "nullable": true}, "bankName": {"type": "string", "nullable": true}, "accountHolderResidentialAddress": {"type": "string", "nullable": true}, "bankBranchName": {"type": "string", "nullable": true}, "bankAddress": {"type": "string", "nullable": true}, "accountNo": {"type": "string", "nullable": true}, "swiftCode": {"type": "string", "nullable": true}, "postCode": {"type": "string", "nullable": true}, "iban": {"type": "string", "nullable": true}, "countryID": {"type": "integer", "format": "int32"}}}]}, "ItalyBankDto": {"allOf": [{"$ref": "#/components/schemas/BasePaymentMethodDto"}, {"type": "object", "additionalProperties": false, "properties": {"accountHolderName": {"type": "string", "nullable": true}, "banKEmail": {"type": "string", "nullable": true}, "iban": {"type": "string", "nullable": true}, "accountHolderResidentialAddress": {"type": "string", "nullable": true}, "postCode": {"type": "string", "nullable": true}, "countryID": {"type": "integer", "format": "int32"}}}]}, "KSABankDto": {"allOf": [{"$ref": "#/components/schemas/BasePaymentMethodDto"}, {"type": "object", "additionalProperties": false, "properties": {"accountHolderName": {"type": "string", "nullable": true}, "bankName": {"type": "string", "nullable": true}, "accountHolderResidentialAddress": {"type": "string", "nullable": true}, "bankBranchName": {"type": "string", "nullable": true}, "bankAddress": {"type": "string", "nullable": true}, "accountNo": {"type": "string", "nullable": true}, "swiftCode": {"type": "string", "nullable": true}, "iban": {"type": "string", "nullable": true}, "countryID": {"type": "integer", "format": "int32"}, "postCode": {"type": "string", "nullable": true}}}]}, "CreditCardFormEGDto": {"allOf": [{"$ref": "#/components/schemas/BasePaymentMethodDto"}, {"type": "object", "additionalProperties": false, "properties": {"accountHolderName": {"type": "string", "nullable": true}, "bankId": {"type": "integer", "format": "int32"}, "branchId": {"type": "integer", "format": "int32"}, "creditCardNo": {"type": "string", "nullable": true}, "swiftCode": {"type": "string", "nullable": true}}}]}, "AmazonGiftCardPaymentDto": {"allOf": [{"$ref": "#/components/schemas/BasePaymentMethodDto"}, {"type": "object", "additionalProperties": false, "properties": {"giftCardStoreId": {"type": "integer", "format": "int32"}, "countryId": {"type": "integer", "format": "int32"}}}]}, "CIBBankTransferDto": {"allOf": [{"$ref": "#/components/schemas/BasePaymentMethodDto"}, {"type": "object", "additionalProperties": false, "properties": {"accountHolderName": {"type": "string", "nullable": true}, "cibAccountNumber": {"type": "string", "nullable": true}, "creditBankID": {"type": "integer", "format": "int32", "nullable": true}, "creditBranchID": {"type": "integer", "format": "int32", "nullable": true}, "creditSWIFTCode": {"type": "string", "nullable": true}}}]}, "SendCashoutCodeDto": {"type": "object", "additionalProperties": false, "properties": {"userMobileNumber": {"type": "string", "nullable": true}, "countryCode": {"type": "integer", "format": "int32"}, "sendMethodId": {"type": "integer", "format": "int32"}}}, "GetAdvertiserDetailsDto": {"allOf": [{"$ref": "#/components/schemas/AdvertiserBaseDto"}, {"type": "object", "additionalProperties": false, "properties": {"title": {"type": "string", "nullable": true}}}]}, "AllStoresSearchDto": {"type": "object", "additionalProperties": false, "properties": {"filter": {"type": "string", "nullable": true}}}, "UserUpdateDto": {"type": "object", "additionalProperties": false, "properties": {"fullName": {"type": "string", "nullable": true}, "gender": {"type": "integer", "format": "int32", "nullable": true}, "birthDate": {"type": "string", "format": "date-time", "nullable": true}}}, "UpdateUserFavDto": {"type": "object", "additionalProperties": false, "properties": {"storeGuid": {"type": "string", "format": "guid"}}}, "SendReferralEmailDto": {"type": "object", "additionalProperties": false, "properties": {"email": {"type": "string", "nullable": true}}}, "DeleteAccountDto": {"type": "object", "additionalProperties": false, "properties": {"reasonId": {"type": "integer", "format": "int32"}, "reasonText": {"type": "string", "nullable": true}}}}, "securitySchemes": {"JWT": {"type": "<PERSON><PERSON><PERSON><PERSON>", "description": "JWT desc", "name": "Authorization", "in": "header"}}}, "security": [{"JWT": []}]}