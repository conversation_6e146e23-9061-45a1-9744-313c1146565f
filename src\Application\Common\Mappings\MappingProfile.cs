﻿using WaffarxWebAPIs.Application.Common.Models;
using WaffarxWebAPIs.Application.Common.Models.Advertisers.AdvertisersDTOs;
using WaffarxWebAPIs.Application.Common.Models.CashBackModels;
using WaffarxWebAPIs.Application.Common.Models.DealsModel;
using WaffarxWebAPIs.Application.Common.Models.ExtenstionModels;
using WaffarxWebAPIs.Application.Common.Models.HelpersModels;
using WaffarxWebAPIs.Application.Common.Models.InStoreOffers;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.AccountDTOs;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.AccountDTOs.EmailSignupModels;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.AccountDTOs.LoginDtos;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.AccountDTOs.SignupWithMobileDto;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.AccountDTOs.SocialLoginDtos;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.UserMainData;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Account;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Advertisers;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Banners;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Cashback;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Cashout;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.FAQ;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Home;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.InstoreModels;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Referal;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Shared;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.User;
using WaffarxWebAPIs.Application.Implementations;
using WaffarxWebAPIs.Domain.Entities;
using WaffarxWebAPIs.Domain.Models.AdvertisersModels;
using WaffarxWebAPIs.Domain.Models.BannersModels;
using WaffarxWebAPIs.Domain.Models.CashBackModels;
using WaffarxWebAPIs.Domain.Models.CashoutModels;
using WaffarxWebAPIs.Domain.Models.Category;
using WaffarxWebAPIs.Domain.Models.CopounsModels;
using WaffarxWebAPIs.Domain.Models.DealModels;
using WaffarxWebAPIs.Domain.Models.FAQModel;
using WaffarxWebAPIs.Domain.Models.GenericModels;
using WaffarxWebAPIs.Domain.Models.InstoreModels;
using WaffarxWebAPIs.Domain.Models.PaymentSettingModels;
using WaffarxWebAPIs.Domain.Models.PromotionModels;
using WaffarxWebAPIs.Domain.Models.ReferalModels;
using WaffarxWebAPIs.Domain.Models.SharedAdvertisersModels;
using WaffarxWebAPIs.Domain.Models.SharedModels;
using WaffarxWebAPIs.Domain.Models.UserModel;
using WaffarxWebAPIs.Domain.Models.UsersModels;

namespace WaffarxWebAPIs.Application.Common.Mappings;
public class MappingProfile : Profile
{

    List<string> SplitText(string text)
    {
        if (text == null)
            return new List<string>();
        return text.Split('|').ToList();
    }


    public MappingProfile()
    {
        var _baseService = new BaseService();

        CreateMap<BannersModel, BannerDto>()
                .ForMember(ad => ad.HeadingText, ar => ar.MapFrom(s => s.HeadingEn))
                .ForMember(ad => ad.ExitClick, ar => ar.MapFrom(s => s.LinkUrl))
                .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                              context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.Id : default))
                .ReverseMap();

        CreateMap<AdvertisersModel, AdvertisersBannersDto>()
               .ForMember(ad => ad.Logo, ar => ar.MapFrom(s => s.WaffarXLogo))
               .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                             context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.ID : default))
               .ReverseMap();

        CreateMap<CouponsMetaDataModel, CouponsMetaDataDto>()
                .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => SplitText(src.Note)))
                .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                                 context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.Id : default))
                .ReverseMap();

        CreateMap<AdvertisersModel, TopStoresDto>()
              .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(s => s.WaffarXLogo))
              .ForMember(ad => ad.AdvertiserLogoPng, ar => ar.MapFrom(s => s.TopStoresLogoPng))
              .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                           context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.ID : default))
              .ReverseMap();
        CreateMap<AdvertisersModel, DoubleCashbackStoreDto>()
              .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(s => s.WaffarXLogo))
              .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                           context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.ID : default))
              .ReverseMap();

        CreateMap<AdvertisersModel, AdvertiserWithExitClickDto>()
                    .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(r => r.WaffarXLogo))
                    .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                    context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.ID : default));

        CreateMap<InStoreStoresModel, InStoreStoreDto>()
                .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(r => r.WaffarXLogo))
                .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                                 context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.Id : default))
                .ReverseMap();

        CreateMap<InStoreStoresModel, InStoreStoreDataDto>()
             .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(r => r.WaffarXLogo))
             .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                              context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.Id : default))
             .ReverseMap();

        CreateMap<AllAdvertisersModel, NewStoresDto>()
              .ForMember(ad => ad.OnlineStore, ar => ar.MapFrom(r => r.OnlineData))
              .ForMember(ad => ad.InStoreStore, ar => ar.MapFrom(r => r.InStoresData))
              .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(r => r.OnlineData.WaffarXLogo))
              .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
              context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.ID : default))
              .ReverseMap();

        CreateMap<AdvertisersModel, AdvertiserDto>()
               .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(r => r.WaffarXLogo))
               .ForMember(ad => ad.AdvertiserLogoPng, ar => ar.MapFrom(r => r.TopStoresLogoPng))
               .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.ID : default))
               .ReverseMap();

        CreateMap<AdvertiserShippingCountriesModel, AdvertiserShippingCountriesDto>()
               .ReverseMap();

        CreateMap<AdvertiserCashBackOptionsModel, CashbackOptionsDto>()
               .ReverseMap();

        CreateMap<AllAdvertisersModel, NewlyAddedStoresDto>()
               .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.ID : default))
               .ReverseMap();

        CreateMap<AdvertisersModel, NewlyAddedStoresDto>()
                .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(s => s.WaffarXLogo))
                .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                                 context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.ID : default))
                .ReverseMap();

        CreateMap<AdvertiserPromotionsModel, HotDealsDto>()
              .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(r => r.AdvertiserData.WaffarXLogo))
              .ForMember(ad => ad.UpToText, ar => ar.MapFrom(r => r.AdvertiserData.UpToText))
              .ForMember(ad => ad.WasText, ar => ar.MapFrom(r => r.AdvertiserData.WasText))
              .ForMember(ad => ad.ExitClick, ar => ar.MapFrom(r => r.AdvertiserData.ExitClick))
              .ForMember(ad => ad.MobExitClick, ar => ar.MapFrom(r => r.AdvertiserData.MobExitClick))
              .ForMember(ad => ad.AdvertiserTitle, ar => ar.MapFrom(r => r.AdvertiserData.AdvertiserTitle))
              .ForMember(ad => ad.AdvertiserName, ar => ar.MapFrom(r => r.AdvertiserData.Advertisername))
              .ForMember(ad => ad.IsCouponOnly, ar => ar.MapFrom(r => r.AdvertiserData.IsCouponOnly))
              .ForMember(ad => ad.StoreGuid, ar => ar.MapFrom(r => r.AdvertiserData.StoreGuid))
              .ForMember(ad => ad.IsHaveWas, ar => ar.MapFrom(r => r.AdvertiserData.IsHaveWas))
              .ForMember(ad => ad.ShopButtonResource, ar => ar.MapFrom(r => r.AdvertiserData.ShopButtonResource))
              .ReverseMap();

        CreateMap<PromotionsModel, PromotionsDto>()
              .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
              context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.Id : default))
              .ReverseMap();

        CreateMap<NewBannersModel, NewBannersDto>()
        .ForMember(ad => ad.Image, ar => ar.MapFrom(r => r.ImageEn))
       .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
        context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.Id : default));

        CreateMap<SignupModel, UserRegisterModel>();

        CreateMap<AppUser, UserRegisterDto>();

        CreateMap<AppUsersModel, UserRegisterDto>();

        CreateMap<SocialLoginDto, LoginDto>();

        CreateMap<SocialLoginDto, UserRegisterModel>()
             .ForMember(ad => ad.registerationId, ar => ar.MapFrom(r => r.typeId));

        // Old

        CreateMap<Resource, ResourcesVM>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                                     context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.Id : default))
                .ReverseMap();

        CreateMap<CouponsMetaData, CouponsMetaDataDto>()
                .ForMember(dest => dest.Notes, opt => opt.MapFrom(src => SplitText(src.Note)))
                .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                                 context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.Id : default))
                .ReverseMap();

        CreateMap<AdvertiserDto, TopStoresDto>()
                .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                                 context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.Id : default))
                .ReverseMap();

        CreateMap<Domain.Entities.Country, CountryDto>().ReverseMap();

        CreateMap<AdvertisersVm, TopStoresDto>()
                .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(s => s.WaffarXLogo))
                .ForMember(ad => ad.AdvertiserLogoPng, ar => ar.MapFrom(s => s.TopStoresLogoPng))
                .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                             context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.ID : default))
                .ReverseMap();

        CreateMap<AdvertisersVm, AdvertiserDto>()
                .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(r => r.WaffarXLogo))
                .ForMember(ad => ad.AdvertiserLogoPng, ar => ar.MapFrom(r => r.TopStoresLogoPng))
                .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                                 context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.ID : default))
                .ReverseMap();
   
        CreateMap<AdvertisersVm, AdvertiserBaseDto>()
                .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(r => r.WaffarXLogo))
                .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                                 context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.ID : default))
                .ReverseMap();

        CreateMap<InStoreStoresVm, InStoreStoreDto>()
                .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(r => r.WaffarXLogo))
                .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                                 context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.Id : default))
                .ReverseMap();
        CreateMap<AdvertiserCashBackOptionsVm, CashbackOptionsDto>()
                .ReverseMap();

        CreateMap<AdvertisersVm, AdvertiserWithExitClickDto>()
        .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(r => r.WaffarXLogo))
        .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                         context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.ID : default));

        CreateMap<InStoreStoreDataDto, InStoreStoreDto>()
            .ForMember(ad => ad.StoreGuid, ar => ar.MapFrom(r => r.StoreGuid))
        .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                         context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.Id : default));

        CreateMap<AdvertiserCopounsModel, AdvertiserCopounDTO>().ReverseMap();

        CreateMap<CopounsModel, CouponCodeDTO>()
                .ForMember(dest => dest.CouponCode, opt => opt.MapFrom(r => r.Couponcode))
                .ForMember(dest => dest.Description, opt => opt.MapFrom(r => r.Description))
                .ForMember(dest => dest.ExitClick, opt => opt.MapFrom(r => r.ExitClick))
                .ForMember(dest => dest.Notes, opt => opt.MapFrom(r => r.Notes))
                .ForMember(dest => dest.ExpireDate, opt=>opt.MapFrom(r=>r.EndDate))
                .ForMember(dest => dest.CreationDate, opt=>opt.MapFrom(r=>r.StartDate))
                .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                         context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.Id : default))
                .ReverseMap();

        CreateMap<CategoryModel, CategoryDTO>()
        .ForMember(dest => dest.CategoryName, opt => opt.MapFrom(r => r.CategoryName))
        .ForMember(dest => dest.CategoryTitle, opt => opt.MapFrom(r => r.CategoryTitle))
        .ForMember(dest => dest.BackgroundColor, opt => opt.MapFrom(r => r.BackgroundColor))
        .ForMember(dest => dest.ImageUrl, opt => opt.MapFrom(r => r.ImageUrl))
        .ForMember(dest => dest.CategoryDescription, opt=> opt.MapFrom(r => r.CategoryDescription))

        .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                 context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.Id : default))
        .ReverseMap();

        CreateMap<CashBackDto, CashBackModel>()
        .ForMember(dest=> dest.DateMilliSecond, opt => opt.MapFrom(r => r.Date))
        .ReverseMap();

        CreateMap<CashoutDto, CashOutModel>()
        .ForMember(dest => dest.PaymentCyclePayDateMilliSecond, opt => opt.MapFrom(r => r.PaymentCyclePayDate))
        .ReverseMap();

        CreateMap<AgcodCashOutDetailsModel, AgcodCashOutDetailsDto>()
        .ForMember(dest => dest.CashOutDate, opt => opt.MapFrom(r => r.CashOutDateSeconds))
        .ReverseMap();

        CreateMap<UserPaymentInfoModel,UserPaymentInfoDto>().ReverseMap();

        CreateMap<UserUpdateDto,UpdateUserModel>().ReverseMap();

        CreateMap<UserCashBackEligibilityModel, UserCashBackEligibilityDto>();
        
        CreateMap<FAQModel, FAQDto>().ReverseMap();
        CreateMap<FAQCategoryModel, FAQCategoryDto>().ReverseMap();

        CreateMap<MobileSignupDto, UserRegisterModel>();

        CreateMap<UsersLinkModel, UsersLinkDto>()
            .ForMember(dest => dest.LinkDate, opt => opt.MapFrom(r => r.LinkDateLong))
            .ForMember(dest => dest.LinkExpireDate, opt => opt.MapFrom(r => r.LinkExpireDateLong));
        CreateMap<ReferalLogDto, ReferalLogModel>()
            .ForMember(dest => dest.CreationDate , opt=>opt.MapFrom(r=>r.CreationDate)).ReverseMap();
        CreateMap<DealPageDto, DealPageModel>().ReverseMap();
        CreateMap<SpecialPageBannerModel, SpecialPageBannerDto>().ReverseMap();
        CreateMap<DealPageBannerModel, DealPageBannerDto>().ReverseMap();
        CreateMap<StaticPageModel, StaticPageDto>().ReverseMap();

        CreateMap<LinkImageModel, LinkImageDto>().ReverseMap();

        CreateMap<PasswordModel, PasswordUpdateModel>().ReverseMap();
        CreateMap<BanksDto,BankModel>().ReverseMap();
        CreateMap<BankBranchDto, BankBranchModel>().ReverseMap();
        CreateMap<WalletDto, WalletModel>().ReverseMap();
        CreateMap<PaymentMethodModel, PaymentMethodDto>().ReverseMap();
        CreateMap<InStoreOffersCardTokenDto,InStoreOffersCardTokenModel>().ReverseMap();
        CreateMap<AppUserDeleteReasonDto, AppUserDeleteReasonModel>().ReverseMap();
        CreateMap<InstoreBranchModel, InstoreBranchDto>().ReverseMap();

        CreateMap<DetailedInstoreStoreDto, InStoreStoresModel>()
            .ForMember(ad => ad.WaffarXLogo, ar => ar.MapFrom(s => s.AdvertiserLogo))
            .ReverseMap();

        CreateMap<AdvertisersModel, AdvertiserHeaderDto>()
                     .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(s => s.WaffarXLogo))
                     .ForMember(ad => ad.AdvertiserType, ar=>ar.MapFrom(s=>s.AdvertiserType))
                     .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
                   context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.ID : default));
        CreateMap<AppUser, AppUsersModel>();


        CreateMap<AdvertisersModel, AdvertiserHeaderWithGuidDto>()
             .ForMember(ad => ad.AdvertiserLogo, ar => ar.MapFrom(s => s.WaffarXLogo))
             .ForMember(ad => ad.AdvertiserType, ar => ar.MapFrom(s => s.AdvertiserType))
             .ForMember(dest => dest.Id, opt => opt.MapFrom((src, dest, destMember, context) =>
           context.Items != null && context.Items.Count > 0 && context.Items.TryGetValue("IncludeId", out var includeId) && (bool)includeId ? src.ID : default));

        CreateMap<VerificationStatusDto, VerificationStatusModel>().ReverseMap();

    }
}
