﻿using Microsoft.AspNetCore.Http;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.AmazonGiftCardPayment;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormNoneEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.CIBBankTransfer;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.CreditCardFormEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.Fawry;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.ItalyBank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.KSABank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.MorocoBank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.Wallet;
using WaffarxWebAPIs.Application.Common.StrategyImplenemtion;
using WaffarxWebAPIs.Application.Interfaces;
using WaffarxWebAPIs.Domain.RepositoryInterface.SharedInterfaces;

namespace WaffarxWebAPIs.Application.Common.Factories;
/// <summary>
/// Factory for creating payment method strategies based on the view model type.
/// </summary>
/// <summary>
/// Factory for creating payment method strategies based on the view model type.
/// </summary>
public static class PaymentMethodStrategyFactory
{
    /// <summary>
    /// Gets the appropriate payment method strategy for the given view model.
    /// </summary>
    /// <param name="viewModel">The view model to get a strategy for</param>
    /// <param name="httpContextAccessor">HTTP context accessor</param>
    /// <param name="resourceService">Resource service</param>
    /// <param name="sharedRepository">Shared repository</param>
    /// <param name="waffarXService">WaffarX service</param>
    /// <returns>The payment method strategy to use</returns>
    /// <exception cref="NotSupportedException">Thrown when the view model type is not supported</exception>
    public static IPaymentMethodStrategy GetStrategy(
        object viewModel,
        IHttpContextAccessor httpContextAccessor,
        IResourceService resourceService,
        ISharedRepository sharedRepository,
        IWaffarXService waffarXService)
    {
        if (viewModel == null)
            throw new ArgumentNullException(nameof(viewModel));

        // Select the appropriate strategy based on view model type
        return viewModel switch
        {
            BankFormEGDto _ => new BankFormEGStrategy(httpContextAccessor, resourceService, sharedRepository, waffarXService),
            FawryDto _ => new FawryStrategy(httpContextAccessor, resourceService, sharedRepository, waffarXService),
            WalletDataDto _ => new WalletStrategy(httpContextAccessor, resourceService, sharedRepository, waffarXService),
            BankFormNoneEGDto _ => new BankFormNoneEGStrategy(httpContextAccessor, resourceService, sharedRepository, waffarXService),
            MorocoBankDto _ => new MoroccoBankStrategy(httpContextAccessor, resourceService, sharedRepository, waffarXService),
            ItalyBankDto _ => new ItalyBankStrategy(httpContextAccessor, resourceService, sharedRepository, waffarXService),
            KSABankDto _ => new KSABankStrategy(httpContextAccessor, resourceService, sharedRepository, waffarXService),
            CreditCardFormEGDto _ => new CreditCardEGStrategy(httpContextAccessor, resourceService, sharedRepository, waffarXService),
            AmazonGiftCardPaymentDto _ => new AGCODStrategy(httpContextAccessor, resourceService, sharedRepository, waffarXService),
            CIBBankTransferDto _ => new CIBBankTransferStrategy(httpContextAccessor, resourceService, sharedRepository, waffarXService),
            _ => throw new NotSupportedException($"Payment method of type {viewModel.GetType().Name} is not supported.")
        };
    }
}
