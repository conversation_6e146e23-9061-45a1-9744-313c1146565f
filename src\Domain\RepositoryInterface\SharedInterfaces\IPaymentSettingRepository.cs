﻿using WaffarxWebAPIs.Domain.Models.PaymentSettingModels;
namespace WaffarxWebAPIs.Domain.RepositoryInterface.SharedInterfaces;
public interface IPaymentSettingRepository
{
    public Task<List<BankModel>> GetBanks(bool isEnglish);
    public Task<List<BankBranchModel>> GetBankBranches(int bankId, bool isEnglish);
    public Task<List<WalletModel>> GetWallets(bool isEnglish);
    public Task<List<Country>> GetCountries();
    public Task<bool> AddOrUpdateUserPaymentInfo(UserPaymentInfo model);
    public Task<UserPaymentInfo> GetUserPaymentInfo(int userId);
    public Task<BankModel> GetBank(int bankId, bool isEnglish);
    public Task<BankBranchModel> GetBankBranch(int branchId, bool isEnglish);
}
