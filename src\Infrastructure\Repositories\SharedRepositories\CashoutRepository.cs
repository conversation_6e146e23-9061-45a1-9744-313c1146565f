﻿using Microsoft.EntityFrameworkCore;
using WaffarxWebAPIs.Application.Common.Behaviours;
using WaffarxWebAPIs.Application.Common.Helpers;
using WaffarxWebAPIs.Application.Common.Models;
using WaffarxWebAPIs.Application.Common.Models.HelpersModels;
using WaffarxWebAPIs.Domain.Entities;
using WaffarxWebAPIs.Domain.Enums;
using WaffarxWebAPIs.Domain.Models.CashBackModels;
using WaffarxWebAPIs.Domain.Models.CashoutModels;
using WaffarxWebAPIs.Domain.RepositoryInterface.SharedInterfaces;
using WaffarxWebAPIs.Infrastructure.Data;

namespace WaffarxWebAPIs.Infrastructure.Repositories.SharedRepositories;
public class CashoutRepository : ICashoutRepository
{
    protected readonly WaffarXDbContext _context;
    private readonly ICashBackRepository _cashbackRepository;
    public CashoutRepository(WaffarXDbContext context, ICashBackRepository cashbackRepository)
    {
        _context = context;
        _cashbackRepository = cashbackRepository;
    }

    public async Task<AgcodCashOutDetailsModel> GetAmazonGiftCardData(int cashoutId, int paymentCycleId, string paymentMethodName, bool isEnglish)
    {
        try
        {
            var result = await _context.AgcodData
         .Join(
             _context.ReferalSettings,
             agc => paymentCycleId,
             rs => rs.Id,
             (agc, rs) => new { agc, rs }
         )
         .Join(
             _context.Advertisers,
             joined => joined.agc.GiftCardStoreId,
             ad => ad.Id,
             (joined, ad) => new { joined.agc, joined.rs, ad }
         )
         .Where(agc => agc.agc.CashOutId == cashoutId && agc.agc.Success == true)
         .Select(data => new AgcodCashOutDetailsModel
         {
             Id = data.agc.Id,
             Amount = data.agc.GiftCardAmount,
             CashOutDate = data.rs.PaymentDate.Value,
             Code = data.agc.GiftCardCode,
             storeId = data.agc.GiftCardStoreId,
             StoreName = isEnglish == true ? data.ad.Advertisername : data.ad.AdvertisernameAr,
             CashOutName = paymentMethodName,
             Image = "https://d3hlclmimgc2c7.cloudfront.net/img/amazongiftcard.png",
         })
         .OrderByDescending(x => x.Id)
         .FirstOrDefaultAsync();

            return result;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<int> GetCashoutCountryById(int cashoutId, int userId)
    {
        try
        {
            var cashoutToGet = await _context.CashOuts.FirstOrDefaultAsync(x => x.Id == cashoutId && x.UserId == userId);
            if (cashoutToGet != null)
            {
                var currency = cashoutToGet.Currency.Trim().ToLower();
                if (currency == "egp")
                {
                    return (int)CountryEnum.Egypt;
                }
                else if (currency == "sar")
                {
                    return (int)CountryEnum.SaudiArabia;
                }
            }
            return (int)CountryEnum.Global;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<List<CashBackModel>> GetCashBackByCashoutId(CashbackInputsModel inputsModel)
    {
        try
        {
            if (inputsModel.CountryId == 0)
            {
                inputsModel.CountryId = await GetCashoutCountryById(inputsModel.CashoutId, inputsModel.UserId);
            }
            var CashBackIDs = await _context.CashOutDetails.AsNoTracking().Where(x => x.CashOutId == inputsModel.CashoutId).Select(x => x.CashBackId).ToListAsync();
            if (inputsModel.SourceId > 0)
            {
                inputsModel.PaymentMethodId = await _context.CashOuts.AsNoTracking().Where(co => co.Id == inputsModel.CashoutId).Select(x => x.PaymentmethodId ?? 0).FirstOrDefaultAsync();
            }

            var cashbacksToGet = await _cashbackRepository.GetCashBacksByCashoutIds(CashBackIDs, inputsModel.IsEnglish);
            if (cashbacksToGet is not null && cashbacksToGet.Count > 0)
            {

                foreach (var data in cashbacksToGet)
                {
                    data.CDateMilliSecond = ParseHelper.ConvertDateTimeToUnix(data.Date);
                    data.DateMilliSecond = ParseHelper.ConvertDateTimeToUnix(data.Date);
                    SetCurrencyValues(data, inputsModel.CountryId, inputsModel.ResourceList);
                }
            }
            return cashbacksToGet;
        }
        catch (Exception)
        {
            throw;
        }
    }
    private void SetCurrencyValues(CashBackModel data, int countryID, Dictionary<string, string> currencySymbolList)
    {
        switch (countryID)
        {
            case (int)CountryEnum.Egypt: // Egypt
                data.OrderValue = (data.OrderValueEGP ?? 0m).ToSimplifiedDecimal();
                data.UserConvertedValue = (data.UserConvertedValueEGP ?? 0m).ToSimplifiedDecimal();
                data.CurrencySymbol = currencySymbolList["EGP"];
                break;
            case (int)CountryEnum.SaudiArabia: // Saudi Arabia
                data.OrderValue = (data.OrderValueSAR ?? 0m).ToSimplifiedDecimal();
                data.UserConvertedValue = (data.UserConvertedValueSAR ?? 0m).ToSimplifiedDecimal();
                data.CurrencySymbol = currencySymbolList["SAR"];
                break;
            default: // Default to USD
                data.OrderValue = (data.OrderValueUSD ?? 0m).ToSimplifiedDecimal();
                data.UserConvertedValue = (data.UserConvertedValueUSD ?? 0m).ToSimplifiedDecimal();
                data.CurrencySymbol = currencySymbolList["USD"];
                break;
        }

        data.OrderCurrencySymbol = data.CurrencySymbol;
    }
    public async Task<CashOutModel> UserCashOutPendingOrRevised(int userId, int paymentCycleId, int cashoutCountryId, bool isEnglish)
    {
        CashOutModel Model = new CashOutModel();
        try
        {

            var cashoutToGet = await _context.CashOuts
                .Include(x => x.Paymentmethod)
                .Where(a => a.UserId == userId && (a.StatusId == 1 || a.StatusId == 2)
                && a.PaymentCycleId <= paymentCycleId)
                .Select(cs => new CashOutModel
                {
                    Id = cs.Id,
                    CDate = cs.Cdate,
                    PaymentCycleId = cs.PaymentCycleId,
                    PaymentMethodId = cs.PaymentmethodId ?? 0,
                    StatusId = cs.StatusId,
                    Total = cs.TotalPaidUsd ?? 0m,
                    TotalUSD = cs.TotalPaidUsd ?? 0m,
                    TotalEGP = cs.TotalPaidEgp ?? 0m,
                    TotalSAR = cs.TotalPaidSar ?? 0m,
                    PaymentMethodName = cs.Paymentmethod != null ? (isEnglish ? cs.Paymentmethod.Name : cs.Paymentmethod.NameAr) : ""
                })
                .FirstOrDefaultAsync();
            if (cashoutToGet != null)
            {
                cashoutToGet.Currency = GetcurrencySympl(cashoutCountryId, isEnglish);
                if (cashoutCountryId == (int)CountryEnum.Egypt)
                {
                    if (cashoutToGet.TotalEGP > 0)
                    {
                        cashoutToGet.Total = cashoutToGet.TotalEGP;
                    }
                    else
                    {
                        Model.Total = Model.TotalUSD;
                        Model.Currency = "$";
                    }
                }
                else if (cashoutCountryId == (int)CountryEnum.SaudiArabia)
                {
                    if (cashoutToGet.TotalSAR > 0)
                    {
                        cashoutToGet.Total = cashoutToGet.TotalSAR;
                    }
                    else
                    {
                        cashoutToGet.Total = cashoutToGet.TotalUSD;
                        cashoutToGet.Currency = "$";
                    }
                }
                else
                {
                    cashoutToGet.Total = cashoutToGet.TotalUSD;
                }
            }
            else
            {
                cashoutToGet = new CashOutModel();
            }
            return cashoutToGet;
        }
        catch (Exception)
        {
            throw;
        }
    }
    private string GetcurrencySympl(int Currency, bool isEnglish)
    {
        if (Currency == (int)CountryEnum.Egypt)
        {
            return isEnglish ? "EGP" : "ج.م";
        }
        else if (Currency == (int)CountryEnum.SaudiArabia)
        {
            return isEnglish ? "SAR" : "ر.س";
        }
        return "$";
    }
    public async Task<VerificationStatusModel> GetCashoutCodeStatus(int userId, string mobileNumber)
    {
        try
        {
            DateTime dateminusweek = DateTime.Now.AddDays(-7);
            VerificationStatusModel verification = new VerificationStatusModel();

            var UserVerficationData = await _context.AppUsersVerificationCashouts.Where(m => m.UserId == userId && m.PhoneNumber == mobileNumber && (m.Cdate <= DateTime.Now && m.Cdate > dateminusweek)).OrderByDescending(x => x.Id).FirstOrDefaultAsync();
            if (UserVerficationData != null && UserVerficationData.IsVerifiedCode == null && UserVerficationData.MsgStatus == true)
            {
                verification.CountryCode = UserVerficationData.CountryCode;
                verification.PhoneNumber = UserVerficationData.PhoneNumber;
                verification.IsSentBefore = true;
            }
            if (UserVerficationData != null && UserVerficationData.IsVerifiedCode == null && (UserVerficationData.MsgStatus == null || UserVerficationData.MsgStatus == false) && UserVerficationData.SenderId == 2)
            {
                verification.CountryCode = UserVerficationData.CountryCode;
                verification.PhoneNumber = UserVerficationData.PhoneNumber;
                verification.IsSentBefore = true;
            }
            return verification;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<int> SetUserCashoutCode(string mobileNumber, int countryCode, int methodId, int userId)
    {
        try
        {
            int res = 0;
            mobileNumber = mobileNumber.Trim();
            if (!string.IsNullOrEmpty(mobileNumber) && mobileNumber.Length > 11 && countryCode == (int)CountryCodeEnum.EgyCode)
            {
                res = -1;
                return res;
            }
            if (!string.IsNullOrEmpty(mobileNumber) && mobileNumber.Length != 9 && countryCode == (int)CountryCodeEnum.KSA)
            {
                res = -1;
                return res;
            }
            DateTime dateminusweek = DateTime.Now.AddDays(-7);
            var LastApprovedData = await _context.AppUsersVerificationCashouts.Where(x => x.UserId == userId
             && (x.Cdate <= DateTime.Now && x.Cdate > dateminusweek)).ToListAsync();
            if (LastApprovedData.Count < 3)
            {
                var ApprovedFromArpu = LastApprovedData.Where(x => x.MsgStatus == true && x.SenderId == 1).ToList();
                var ApprovedFromAms = LastApprovedData.Where(x => x.MsgId != null && x.SenderId == 2).ToList();

                if (ApprovedFromArpu.Count() < 2 || ApprovedFromAms.Count() < 1)
                {

                    var code = await GetRandomCashoutMobileCode(6);
                    if (LastApprovedData.Count < 2)
                    {
                        bool SendResult = await SendCashoutCodeArpu(mobileNumber, userId, code, countryCode, methodId);
                        if (SendResult == true)
                            res = 1;
                    }
                }
                else
                {
                    res = -2;
                }
            }
            else
            {
                res = -2;
            }
            return res;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<bool> SendCashoutCodeArpu(string MobNo, int UserId, string Code, int CountryCode, int MethodId)
    {
        bool res = false;
        try
        {
            var TextMsg = "Your confirmation code is : " + Code;
            ArpuResponse RetVal = await RequestsHelper.SendSmsArpuPlus(CountryCode, MobNo.Trim(), TextMsg);
            AppUsersVerificationCashout appUsersVerificationCashout = new AppUsersVerificationCashout
            {
                UserId = UserId,
                PhoneNumber = MobNo,
                SenderId = 1,
                MsgStatus = RetVal.status,
                Code = Code,
                CountryCode = CountryCode,
                MsgId = RetVal.message_id.ToString(),
                Cdate = DateTime.Now,
                MethodId = MethodId

            };
            await _context.AppUsersVerificationCashouts.AddAsync(appUsersVerificationCashout);
            await _context.SaveChangesAsync();
            if (RetVal.status == true)
            {
                res = true;
            }
            return res;
        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<string> GetRandomCashoutMobileCode(int lenght)
    {
        try
        {
            string code = ParseHelper.RandomCashoutMobileCode(lenght);
            var existedBefore = await _context.AppUsersVerificationCashouts.Where(a => a.Code == code).AnyAsync();
            if (existedBefore)
            {
                code = await GetRandomCashoutMobileCode(lenght);
            }
            return code;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<bool> CheckUserCashoutCode(int userId, string code, int methodId)
    {
        try
        {
            AppUsersVerificationCashout MobileVerification = _context.AppUsersVerificationCashouts.Where(x => x.UserId == userId).OrderByDescending(i => i.Id).FirstOrDefault();
            if (MobileVerification != null)
            {
                if (MobileVerification.Code.Trim() == code.Trim())
                {
                    MobileVerification.IsVerifiedCode = true;
                    MobileVerification.MethodId = methodId;

                    _context.AppUsersVerificationCashouts.Update(MobileVerification);
                    return await _context.SaveChangesAsync() > 0;
                }
            }
            return false;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<bool> CheckUserCashoutVerifiedNumber(string mobileNumber, int userId)
    {
        try
        {
            var verifiedNumberToGet = await _context.AppUsersVerificationCashouts.Where(x => x.UserId == userId && x.PhoneNumber == mobileNumber && x.IsVerifiedCode == true && x.MsgStatus == true).FirstOrDefaultAsync();
            return verifiedNumberToGet != null;
        }
        catch (Exception)
        {
            throw;
        }

    }

    public async Task UpdateLastPendingCashoutPaymentMethod(int userId, int paymentMethodId)
    {
        try
        {
            var LastPendingCashOut = await _context.CashOuts.Where(a => a.UserId == userId && a.StatusId == 1).FirstOrDefaultAsync();
            if (LastPendingCashOut != null)
            {
                LastPendingCashOut.PaymentmethodId = paymentMethodId;
                _context.CashOuts.Update(LastPendingCashOut);
                await _context.SaveChangesAsync();
            }
        }
        catch(Exception)
        {
            throw;
        }
    }
}
