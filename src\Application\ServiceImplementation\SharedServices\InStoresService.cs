﻿using Microsoft.AspNetCore.Http;
using WaffarxWebAPIs.Application.Common.Models.Advertisers.AdvertisersDTOs;
using WaffarxWebAPIs.Application.Common.Models;
using WaffarxWebAPIs.Domain.Interfaces.SharedInterfaces;
using WaffarxWebAPIs.Domain.Models.GenericModels;
using WaffarxWebAPIs.Domain.Models.InstoreModels;
using WaffarxWebAPIs.Domain.Constants;
using WaffarxWebAPIs.Domain.Enums;
using WaffarxWebAPIs.Application.Interfaces;
using WaffarxWebAPIs.Application.Interfaces.SharedInterfaces;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.InstoreModels;
using WaffarxWebAPIs.Application.Common.Helpers;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.AdvertiserBase;
using WaffarxWebAPIs.Domain.Models.Category;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.StoresByCategory;
using WaffarxWebAPIs.Domain.Entities;

namespace WaffarxWebAPIs.Application.Implementations.SharedServices;
public class InStoresService : BaseService, IInStoresService
{
    private readonly IInstoreRepository _instoreRepository;
    private readonly IResourceService _resourceService;
    private readonly IBonusService _bonusService;
    private readonly IMapper _mapper;
    private readonly IUserRepository _userRepository;
    private readonly IWaffarXService _waffarxService;

    private const int CREDIT_CARD_TYPE = 1;
    private const int DEBIT_CARD_TYPE = 2;
    public InStoresService(IInstoreRepository instoreRepository
        , IResourceService resourceService, IBonusService bonusService
        , IMapper mapper
        , IHttpContextAccessor httpContextAccessor, IUserRepository userRepository, IWaffarXService waffarxService) : base(httpContextAccessor)
    {
        _instoreRepository = instoreRepository;
        _resourceService = resourceService;
        _bonusService = bonusService;
        _mapper = mapper;
        _userRepository = userRepository;
        _waffarxService = waffarxService;
    }

    public async Task<GenericResponse<DisableInStoreCardDto>> DisableInStoreCard(int cardId)
    {
        var response = new GenericResponse<DisableInStoreCardDto>();
        try
        {
            var card = await _instoreRepository.GetCardById(cardId, UserId ?? 0);
            if (card == null)
            {
                response.Status = StaticValues.Error;
                response.Data = new DisableInStoreCardDto();
                return response;
            }

            bool isCibUser = await CheckIsCibUser();
            if (isCibUser)
            {
                return await CheckCardDisablingRestrictions(card);
            }

            await _instoreRepository.DisableCard(card, UserId ?? 0);

            response.Status = StaticValues.Success;
            response.Data = new DisableInStoreCardDto();
            return response;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<List<InStoreStoreDataDto>>> GetHomeInStoreStores(int pageNo = 1, int pageSize = 12)
    {
        var result = new GenericResponse<List<InStoreStoreDataDto>>();
        var list = new PaginationResultModel<List<InStoreStoresModel>>();
        try
        {
            list = await _instoreRepository.HomeInStoreStores(UserId ?? 0, pageNo, pageSize);
            if (list != null && list.Data.Count > 0)
            {
                List<string> ResourceKeys = new List<string>() { "WasText", "storeDisabledAccount", "AddReceipt", "Link" };
                var ResourceList = await _resourceService.GetResourcesList(ResourceKeys);

                string DisabledMsg = "";
                var DisabledInStoreStores = await _instoreRepository.UserInStoreDisabledStores(UserId ?? 0);
                if (DisabledInStoreStores.Count > 0)
                    DisabledMsg = ResourceList["storeDisabledAccount"];

                var updatedStores = await _bonusService.CalculatesInstoreBonus(list.Data);

                var GasAvailability = await UserGasOrderCountRemaining();

                foreach (InStoreStoresModel store in list.Data)
                {
                    var Updatedstore = updatedStores.Where(s => s.Id == store.Id).FirstOrDefault();
                    store.UpTo = Updatedstore.UpTo;
                    store.Was = Updatedstore.Was;
                    store.WaffarXLogo = Updatedstore.WaffarXLogo;
                    store.IsHaveWas = Updatedstore.IsHaveWas;
                    store.UserCommission = Updatedstore.UserCommission;
                    store.UserPercentage = Updatedstore.UserPercentage;
                    store.UpToText = (!string.IsNullOrEmpty(Updatedstore.UpToText) && Updatedstore.UpToText.ToLower().Contains("up")) ? Updatedstore.UpToText.Replace(" cash back", "") : Updatedstore.UpToText;
                    store.WasText = Updatedstore.WasText;
                    if (store.UserPercentage == 100)
                    {
                        store.IsHaveWas = true;
                        if (store.Id != (int)SpecialStoresEnum.Amplifon)
                            store.Was = Updatedstore.UpTo / 2;

                        if (store.IsFlat == true)
                            store.WasText = ResourceList["WasText"] + " " + (!string.IsNullOrEmpty(store.Currency) ? store.Currency : "$") + store.Was.ToSimplifiedString();
                        else
                            store.WasText = ResourceList["WasText"] + " " + store.Was.ToSimplifiedString() + "% ";
                    }

                    if (store.InstoreBranches != null && store.InstoreBranches.Count > 0)
                    {
                        foreach (var branch in store.InstoreBranches)
                        {
                            branch.BranchName = string.IsNullOrEmpty(branch.BranchName) ? string.Empty : (IsEnglish ? branch.BranchName : branch.BranchNameAr);
                            branch.Address = string.IsNullOrEmpty(branch.Address) ? string.Empty : (IsEnglish ? branch.Address : branch.AddressAr);
                            branch.UserWarning = string.IsNullOrEmpty(branch.UserWarning) ? string.Empty : (IsEnglish ? branch.UserWarning : branch.UserWarningAr);
                        }
                    }

                    store.IsNew = false;
                    store.WaffarXLogo = !string.IsNullOrEmpty(store.WaffarXLogo) ? (AppSettings.WaffarXSettings.BaseImgUrl + StaticValues.StoreLogos + store.WaffarXLogo).ToLower() : AppSettings.WaffarXSettings.ErrorPage;
                    store.Advertisername = (IsEnglish ? store.Advertisername : store.AdvertisernameAr);
                    store.Terms = (IsEnglish ? store.Terms : store.TermsAr);
                    store.MerchantReceipt = !string.IsNullOrEmpty(store.MerchantReceipt) ? AppSettings.WaffarXSettings.BaseImgUrl + StaticValues.InStoreReceipts + store.MerchantReceipt : string.Empty;
                    store.MerchantBankReceipt = !string.IsNullOrEmpty(store.MerchantBankReceipt) ? AppSettings.WaffarXSettings.BaseImgUrl + StaticValues.InStoreReceipts + store.MerchantBankReceipt : string.Empty;
                    store.Description = IsEnglish ? store.Description : store.DescriptionAr;
                    store.UserWarning = IsEnglish ? store.UserWarning : store.UserWarningAr;

                    store.UserDisabledStore = (DisabledInStoreStores.Any(x => x == store.Id)) ? true : false;
                    store.UserDisabledStoreMsg = DisabledMsg;

                    if (store.Id == (int)SpecialStoresEnum.Gas && !store.UserDisabledStore && GasAvailability?.Allowed == false)
                    {
                        store.UserDisabledStore = true;
                        store.UserDisabledStoreMsg = GasAvailability.ErrorMsg;
                    }
                    store.ShopButtonResource = store.IsLinked ? ResourceList["AddReceipt"] : ResourceList["Link"];

                }

                result.Data = _mapper.Map<List<InStoreStoresModel>, List<InStoreStoreDataDto>>(list.Data, opts => opts.Items["IncludeId"] = false);
                result.Status = StaticValues.Success;
                result.TotalCount = list.TotalRecords;
                return result;
            }
            result.Data = new List<InStoreStoreDataDto>();
            result.Status = StaticValues.Error;
            result.TotalCount = list.TotalRecords;
            return result;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GasCheckModel> UserGasOrderCountRemaining()
    {
        GasCheckModel gasCheck = new GasCheckModel();
        //Check if User Allowed
        var TotalOrdersUserAllowedToAdd = await _instoreRepository.CheckGasOrderCountRemaining(UserId ?? 0);
        if (TotalOrdersUserAllowedToAdd != null)
        {
            if (TotalOrdersUserAllowedToAdd.GasOrdersCurrentMonth >= 8)
            {
                gasCheck.Allowed = false;
                gasCheck.ErrorMsg = await _resourceService.GetResourceByKey("reached_8");
            }
            else if (TotalOrdersUserAllowedToAdd.GasOrdersCurrentMonth < 8 && TotalOrdersUserAllowedToAdd.TotalAllowedGasOrders == 0)
            {
                gasCheck.Allowed = false;
                gasCheck.ErrorMsg = await _resourceService.GetResourceByKey("reached_4_but_not_8");
            }
            else
            {
                gasCheck.Allowed = true;
            }
        }
        else
        {
            gasCheck.Allowed = true;
        }

        return gasCheck;
    }
    public async Task<List<int>> UserInStoreDisabledStores(int userId)
    {
        return await _instoreRepository.UserInStoreDisabledStores(UserId ?? 0);
    }
    public async Task<GenericResponse<List<InStoreCardDto>>> GetInstoreOfferCards()
    {
        List<InStoreCardDto> cards = new List<InStoreCardDto>();
        try
        {
            var cardTokens = await _instoreRepository.GetUserCardTokens(UserId ?? 0);
            if (cardTokens?.Count > 0)
            {
                foreach (var card in cardTokens)
                {
                    var instoreCard = new InStoreCardDto();

                    // Map other properties
                    instoreCard.Id = card.RowId;
                    instoreCard.MaskedPan = card.MaskedPan;
                    instoreCard.Provider = card.Provider;
                    instoreCard.DateCreated = card.CreatedAt;
                    instoreCard.CardSubtype = card.CardSubtype;
                    instoreCard.SubTypeMaskedPan = card.CardSubtype + " " + card.MaskedPan;
                    instoreCard.LinksCount = await _instoreRepository.GetCardLinksCount(card.RowId);

                    // Set card image URL
                    SetCardImage(instoreCard);

                    instoreCard.CanBeDeleted = true;
                    cards.Add(instoreCard);
                }
                return new GenericResponse<List<InStoreCardDto>>()
                {
                    Status = StaticValues.Success,
                    Data = cards,
                };
            }
            return new GenericResponse<List<InStoreCardDto>>()
            {
                Status = StaticValues.Error,
                Data = new List<InStoreCardDto>(),
            };

        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<InstoreUsersLinkDto>> GetInstoreLinksHistory(int pageNo = 1, int pageSize = 10)
    {
        // Get count to check if we have any results
        int totalRows = await _instoreRepository.CountUserLinks(UserId ?? 0, IsEnglish);

        if (totalRows == 0)
        {
            return new GenericResponse<InstoreUsersLinkDto>
            {
                Status = StaticValues.Success,
                Data = new InstoreUsersLinkDto 
                {
                    UsersLinks = new List<UsersLinkDto>(),
                    IsHaveLinksBefore = await _instoreRepository.AnyLinksByUserId(UserId ?? 0)
                }

            };
        }

        // Get the paginated data
        var resultLinks = await _instoreRepository.GetUserLinks(UserId ?? 0, IsEnglish, pageNo, pageSize);

        // Apply business logic transformations
        foreach (var item in resultLinks)
        {
            await TransformLinkData(item, IsEnglish);
            item.LinkDateLong = ParseHelper.ConvertDateTimeToUnix(item.LinkDate);
            item.LinkExpireDateLong = ParseHelper.ConvertDateTimeToUnix(item.LinkExpireDate);
        }
        return new GenericResponse<InstoreUsersLinkDto>
        {
            Status = StaticValues.Success,
            Data = new InstoreUsersLinkDto
            {
                UsersLinks = _mapper.Map<List<UsersLinkDto>>(resultLinks, opts => opts.Items["IncludeId"] = false),
                IsHaveLinksBefore = await _instoreRepository.AnyLinksByUserId(UserId ?? 0)
            },
            TotalCount = totalRows
            

        };

    }
    public async Task<GenericResponse<InstoreUsersLinkDto>> GetInstoreLinksOngoing(int pageNo = 1, int pageSize = 10)
    {
        try
        {
            var resultLinks = await _instoreRepository.GetUserInStoreOngoingLink(UserId ?? 0, IsEnglish, pageNo, pageSize);
            if (!resultLinks.Data.Any())
            {
                return new GenericResponse<InstoreUsersLinkDto>
                {
                    Status = StaticValues.Error,
                    Data = new InstoreUsersLinkDto
                    {
                        UsersLinks = new List<UsersLinkDto>(),
                        IsHaveLinksBefore = await _instoreRepository.AnyLinksByUserId(UserId ?? 0),
                    },

                };
            }
            var parentIds = resultLinks.Data.Select(x => x.Id).ToList();
            if (parentIds.Count == 0)
            {
                return new GenericResponse<InstoreUsersLinkDto>
                {
                    Status = StaticValues.Error,
                    Data = new InstoreUsersLinkDto
                    {
                        UsersLinks = new List<UsersLinkDto>(),
                        IsHaveLinksBefore = await _instoreRepository.AnyLinksByUserId(UserId ?? 0),
                    },
                    TotalCount = resultLinks.TotalRecords

                };

            }
            var resourceList = await _resourceService.GetResourcesList(new List<string> { "InstoreUnderReviewCashback", "InstoreFailedOrder", "NotValidOrderId", "NotClearPOSImage", "NotValidOrderReceipt", "InValidSMSImage" });
            foreach (var item in resultLinks.Data)
            {
                var endOfDay = item.LinkDate.Date.AddDays(1 + item.LinkDurationSettings).AddMinutes(-1);
                item.DateDiff = (int)(endOfDay - DateTime.Now).TotalHours;
                item.LinkDateLong = ParseHelper.ConvertDateTimeToUnix(item.LinkDate);
                item.LinkExpireDateLong = ParseHelper.ConvertDateTimeToUnix(item.LinkExpireDate);

                if (item.StatusID == 1)
                {
                    item.CanAddMore = true;
                }
                item.LinkMessages = new List<string>() { };
                if (item.DateDiff > 96)
                {
                    item.LinkStatus = 1;
                }
                else if (item.DateDiff > 24 && item.DateDiff <= 96)
                {
                    item.LinkStatus = 2;
                }
                else if (item.DateDiff > 0 && item.DateDiff <= 24)
                {
                    item.LinkStatus = 3;
                }

                if (item.StatusID == 2)
                {
                    item.LinkMessages = new List<string> { resourceList["InstoreUnderReviewCashback"]};
                }
                else if (item.StatusID == 4)
                {
                    item.LinkMessages = new List<string> { resourceList["InstoreFailedOrder"] };
                }
                else if (item.StatusID == 5)
                {
                    foreach (var err in item.FailureReasons)
                    {
                        if (err == 1)
                        {
                            item.LinkMessages.Add(resourceList["NotValidOrderId"]);
                        }
                        else if (err == 2)
                        {
                            item.LinkMessages.Add(resourceList["NotClearPOSImage"]);
                        }
                        else if (err == 12)
                        {
                            item.LinkMessages.Add(resourceList["InValidSMSImage"]);
                        }
                        else
                        {
                            item.LinkMessages.Add(resourceList["NotValidOrderReceipt"]);
                        }
                    }
                }
            }

            return new GenericResponse<InstoreUsersLinkDto>
            {
                Status = StaticValues.Success,
                Data = new InstoreUsersLinkDto
                {
                    UsersLinks = _mapper.Map<List<UsersLinkDto>>(resultLinks.Data, opts => opts.Items["IncludeId"] = false),
                    IsHaveLinksBefore = await _instoreRepository.AnyLinksByUserId(UserId ?? 0),
                },
                TotalCount = resultLinks.TotalRecords
            };



        }
        catch(Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<DetailedInstoreStoreDto>> GetInstoreStoreById(Guid? storeId)
    {
        try
        {
            InStoreStoresModel result = await BaseGetInstoreStoreById(storeId.Value);
            result.Description = IsEnglish ? result.Description : result.DescriptionAr;
            result.UserWarning = IsEnglish ? result.UserWarning : result.UserWarningAr;
            result.Terms = IsEnglish ? result.Terms : result.TermsAr;
            result.MerchantReceipt = !string.IsNullOrEmpty(result.MerchantReceipt) ? AppSettings.WaffarXSettings.BaseImgUrl + StaticValues.InStoreReceipts + result.MerchantReceipt : string.Empty;
            result.MerchantBankReceipt = !string.IsNullOrEmpty(result.MerchantBankReceipt) ? AppSettings.WaffarXSettings.BaseImgUrl + StaticValues.InStoreReceipts + result.MerchantBankReceipt : string.Empty;
            if (result.IsHaveBranches)
            {
                foreach (var item in result.InstoreBranches)
                {
                    item.UserWarning = IsEnglish ? item.UserWarning : item.UserWarningAr;
                    item.BranchName = IsEnglish ? item.BranchName : item.BranchNameAr;
                }
            }
            else
            {
                result.InstoreBranches = null;
            }
            
            if (result == null)
            {
                return new GenericResponse<DetailedInstoreStoreDto>
                {
                    Status = StaticValues.Error,
                    Data = new DetailedInstoreStoreDto()
                };
            }

            return new GenericResponse<DetailedInstoreStoreDto>
            {
                Status = StaticValues.Success,
                Data = _mapper.Map<DetailedInstoreStoreDto>(result, opts => opts.Items["IncludeId"] = false)
            };
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<bool>> LinkUserToInstoreStore(AdvertiserBaseDto model)
    {
        try
        {
            var userToGet = await _userRepository.GetUserById(UserId ?? 0);
            var storeDataToGet = await BaseGetInstoreStoreById(model.advertiserId.Value);
            if (storeDataToGet != null)
            {
                if (!storeDataToGet.UserDisabledStore)
                {
                    var duration = storeDataToGet.OfferDuration.HasValue ? storeDataToGet.OfferDuration.Value : 0;
                    var linkedStore = await _instoreRepository.UserLinkedStore(UserId ?? 0, storeDataToGet.Id, duration);
                    if (linkedStore == null)
                    {
                        InstoreLinkStoreModel cardModel = new InstoreLinkStoreModel
                        {
                            StoreId = storeDataToGet.Id,
                            UserCommision = storeDataToGet.UserCommission,
                            UserId = UserId ?? 0,
                            UserPercentage = storeDataToGet.UserPercentage,
                        };

                        var result = await _instoreRepository.InsertInstoreCardLink(cardModel);
                        if (result > 0)
                        {
                            return new GenericResponse<bool>
                            {
                                Data = true,
                                Status = StaticValues.Success,
                            };
                        }
                    }
                    return new GenericResponse<bool>
                    {
                        Data = false,
                        Status = StaticValues.Error,
                        Errors = new List<string>() {await _resourceService.GetResourceByKey("ErrorHappendWhileSaving") }
                    };
                }
                return new GenericResponse<bool>
                {
                    Data = false,
                    Status = StaticValues.Error,
                    Errors = new List<string>() { await _resourceService.GetResourceByKey("storeDisabledAccount") }
                };
            }

            return new GenericResponse<bool>
            {
                Data = false,
                Status = StaticValues.Error,
            };

        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<UsersLinkDetailsDto>> GetUserInStoreLink(long id)
    {
        try
        {
           var parentLink =  await _instoreRepository.GetParentInstoreAdvertiserLink(UserId ?? 0, IsEnglish, id);
            if(parentLink == null)
            {
                return new GenericResponse<UsersLinkDetailsDto>
                {
                    Status = StaticValues.Error,
                    Data = new UsersLinkDetailsDto()
                };
            }
            UsersLinkDetailsDto userLinkDetailsDto = new UsersLinkDetailsDto();
            var resourceList = await _resourceService.GetResourcesList(new List<string> { "InstoreUnderReviewCashback", "InstoreFailedOrder", "NotValidOrderId", "NotClearPOSImage", "NotValidOrderReceipt", "InValidSMSImage" });

            if (parentLink.StatusID == 2)
            {
                userLinkDetailsDto.LinkMessages = new List<string> { resourceList["InstoreUnderReviewCashback"] };
            }
            else if (parentLink.StatusID == 4)
            {
                userLinkDetailsDto.LinkMessages = new List<string> { resourceList["InstoreFailedOrder"] };
            }
            else if (parentLink.StatusID == 5)
            {
                foreach (var err in parentLink.FailureReasons)
                {
                    if (err == 1)
                    {
                        userLinkDetailsDto.LinkMessages.Add(resourceList["NotValidOrderId"]);
                    }
                    else if (err == 2)
                    {
                        userLinkDetailsDto.LinkMessages.Add(resourceList["NotClearPOSImage"]);
                    }
                    else if (err == 12)
                    {
                        userLinkDetailsDto.LinkMessages.Add(resourceList["InValidSMSImage"]);
                    }
                    else
                    {
                        userLinkDetailsDto.LinkMessages.Add(resourceList["NotValidOrderReceipt"]);
                    }
                }
            }
            userLinkDetailsDto.StatusId = parentLink.StatusID;
            userLinkDetailsDto.AdvertiserName = parentLink.AdvertiserName;
            var childLinks = await _instoreRepository.GetChildInstoreAdvertiserLink(UserId ?? 0, IsEnglish, parentLink.Id);

            if (childLinks != null) {
                userLinkDetailsDto.ChildLinks = _mapper.Map<List<UsersLinkDto>>(childLinks, opts => opts.Items["IncludeId"] = false);
            }
            return new GenericResponse<UsersLinkDetailsDto>
            {
                Status = StaticValues.Success,
                Data = userLinkDetailsDto

            };

        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<List<InStoreStoreDataDto>>> GetAllNewlyAddedInstore(StoresByCategoryFiltersDTO categoryFiltersDTO)
    {
        try
        {
            StoreCategoryFiltersModel filtersModel = new StoreCategoryFiltersModel();
            var bankId = _waffarxService.BankUserData()?.BankId;
            filtersModel = filtersModel.Mapping<StoreCategoryFiltersModel>(categoryFiltersDTO, bankId ?? 0, IsEnglish);
            filtersModel.userId = UserId ?? 0;
            filtersModel.countryId = CountryId;
            var queryResult = await _instoreRepository.GetNewlyAddedInStore(filtersModel);
            if (queryResult == null)
            {
                return new GenericResponse<List<InStoreStoreDataDto>>
                {
                    Status = StaticValues.Error,
                    Data = new List<InStoreStoreDataDto>()
                };
            }
            queryResult.Data = await setInstoreStoresModel(queryResult.Data);
            return new GenericResponse<List<InStoreStoreDataDto>>
            {
                Data = _mapper.Map<List<InStoreStoresModel>, List<InStoreStoreDataDto>>(queryResult.Data, opts => opts.Items["IncludeId"] = false),
                Status = StaticValues.Success,
                TotalCount = queryResult.TotalRecords
            };
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<InStoreStoreDataWithFilterDto>> GetAllInStoreHome(StoresByCategoryFiltersDTO categoryFiltersDTO)
    {
        try
        {
            StoreCategoryFiltersModel filtersModel = new StoreCategoryFiltersModel();
            var bankId = _waffarxService.BankUserData()?.BankId;
            filtersModel = filtersModel.Mapping<StoreCategoryFiltersModel>(categoryFiltersDTO, bankId ?? 0, IsEnglish);
            filtersModel.userId = UserId ?? 0;
            filtersModel.countryId = CountryId;
            var queryResult = await _instoreRepository.GetAllInStoreHome(filtersModel);
            if (queryResult == null)
            {
                return new GenericResponse<InStoreStoreDataWithFilterDto>
                {
                    Status = StaticValues.Error,
                    Data = new InStoreStoreDataWithFilterDto()
                };
            }
            queryResult.Data = await setInstoreStoresModel(queryResult.Data);
            return new GenericResponse<InStoreStoreDataWithFilterDto>
            {
                Data = new InStoreStoreDataWithFilterDto
                {
                   Data = _mapper.Map<List<InStoreStoresModel>, List<InStoreStoreDataDto>>(queryResult.Data, opts => opts.Items["IncludeId"] = false),
                   Filters = await setAllStorePageFilters()
                } ,
                Status = StaticValues.Success,
                TotalCount = queryResult.TotalRecords
            };
        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<UsersLinkModel> TransformLinkData(UsersLinkModel link, bool isEnglish)
    {
        if (link.IsParent && (link.InvoiceImages.Count() > 0 || link.POSImages.Count() > 0))
        {
            link.ParentId = link.Id;
        }

        link.LinkExpireDate = link.LinkDate.Date.AddDays(1).AddTicks(-1).AddDays(link.LinkDurationSettings);
        link.LinkMessages = new List<string>() { };
        link.RejectedLinkMessages = new List<string>() { };

        // Add appropriate messages based on status
        if (link.StatusID == 2)
        {
            link.LinkMessages = new List<string> { await _resourceService.GetResourceByKey("InstoreUnderReviewCashback") };
        }
        else if (link.StatusID == 4)
        {
            var resourceList  = await _resourceService.GetResourcesList(new List<string> { "instoreClickHereToRejected", "CardYouPreviouslyPaid", "CashBackWillReflectOnAccount", "receiptDoesnotMatchStore", "receiptBeforeOffer" });
            link.LinkMessages = new List<string> { resourceList["instoreClickHereToRejected"] };
            foreach (var err in link.FailureReasons)
            {
                if (err == 4)
                {
                    link.RejectedLinkMessages.Add(resourceList["CardYouPreviouslyPaid"]);
                    link.RejectedLinkMessages.Add(resourceList["CashBackWillReflectOnAccount"]);
                }
                else if (err == 5)
                {
                    link.RejectedLinkMessages.Add(resourceList["receiptDoesnotMatchStore"]);
                }
                else if (err == 9)
                {
                    link.RejectedLinkMessages.Add(resourceList["receiptBeforeOffer"]);
                }
            }
        }
        else if (link.StatusID == 5)
        {
            var resourceList = await _resourceService.GetResourcesList(new List<string> { "NotValidOrderId", "NotClearPOSImage", "NotValidOrderReceipt" });
            foreach (var err in link.FailureReasons)
            {
                if (err == 1)
                {
                    link.LinkMessages.Add(resourceList["NotValidOrderId"]);
                }
                else if (err == 2)
                {
                    link.LinkMessages.Add(resourceList["NotClearPOSImage"]);
                }
                else if (err == 3)
                {
                    link.LinkMessages.Add(resourceList["NotValidOrderReceipt"]);
                }
            }
        }
        return link;
    }
    private async Task<GenericResponse<DisableInStoreCardDto>> CheckCardDisablingRestrictions(InStoreOffersCardTokenModel card)
    {
        var response = new GenericResponse<DisableInStoreCardDto>();
        try
        {
            var resourceList = await _resourceService.GetResourcesList(new List<string> { "CibCreditDeleteCardMsg", "CibDeleteCardMsg", "CibDeleteCardCSMsg", "ErrorHappend" });

            var userPaymentInfo = await _userRepository.GetUserPaymentInfo(UserId ?? 0, IsEnglish);
            if (userPaymentInfo == null)
            {
                response.Status = StaticValues.Error;
                response.Errors = new List<string>() { resourceList["CibCreditDeleteCardMsg"] };
                return response;
            }

            int cardType = await _instoreRepository.GetCardTypeByBin((int)BankEnum.CIB, card.MaskedPan);
            var userCards = await GetUserCardsWithTypes();
            if (card.MerchantId == null && card.Provider != "Apple Pay Dan Number")
            {
                // Check credit card payment method restrictions
                if (userPaymentInfo.PaymentMethodId == (int)PaymentMethodEnum.CIBCard)
                {
                    if (cardType == CREDIT_CARD_TYPE && card.MaskedPan == userPaymentInfo.CreditCardMaskedNumber)
                    {
                        int creditCardsCount = userCards.Count(c => c.CardTypeId == CREDIT_CARD_TYPE);

                        if (creditCardsCount > 1)
                        {
                            response.Data =  new DisableInStoreCardDto
                            {
                                AddCardMsg = resourceList["CibCreditDeleteCardMsg"],
                                ContactCsMsg = "",
                                SwithCard = true
                            };
                            response.Status = StaticValues.Error;
                            return response;
                        }
                        else
                        {
                            response.Data =  new DisableInStoreCardDto
                            {
                                AddCardMsg = resourceList["CibDeleteCardMsg"],
                                ContactCsMsg = resourceList["CibDeleteCardCSMsg"],
                                SwithCard = false
                            };
                            response.Status = StaticValues.Error;
                            return response;
                        }
                    }
                }
                // Check debit card payment method restrictions
                if (userPaymentInfo.PaymentMethodId == (int)PaymentMethodEnum.CIBBankTransfer)
                {
                    if (cardType == DEBIT_CARD_TYPE)
                    {
                        int debitCardsCount = userCards.Count(c => c.CardTypeId == DEBIT_CARD_TYPE);
                        int creditCardsCount = userCards.Count(c => c.CardTypeId == CREDIT_CARD_TYPE);

                        if (debitCardsCount == 1 && userCards.Count == 1)
                        {
                            response.Data = new DisableInStoreCardDto
                            {
                                AddCardMsg = resourceList["CibDeleteCardMsg"],
                                ContactCsMsg = resourceList["CibDeleteCardCSMsg"],
                                SwithCard = false
                            };
                            response.Status = StaticValues.Error;
                            return response;
                        }
                        else if (debitCardsCount == 1 && creditCardsCount > 0)
                        {
                            response.Data = new DisableInStoreCardDto
                            {
                                AddCardMsg = resourceList["CibCreditDeleteCardMsg"],
                                ContactCsMsg = "",
                                SwithCard = true
                            };
                            response.Status = StaticValues.Error;
                            return response;

                        }
                    }
                }
            }
            await _instoreRepository.DisableCard(card, UserId ?? 0);
            response.Status = StaticValues.Success;
            return response;
        }
        catch (Exception)
        {
            throw;
        }
       
    }
    private void SetCardImage(InStoreCardDto card)
    {
        string cardType = card.CardSubtype?.ToLower() ?? "";

        if (string.Equals(cardType, "mastercard", StringComparison.OrdinalIgnoreCase))
        {
            card.ImageUrl = AppSettings.WaffarXSettings.BaseImgUrl + "img/mastercardimg1.png";
        }
        else if (string.Equals(cardType, "meeza", StringComparison.OrdinalIgnoreCase) ||
                 string.Equals(cardType, "maestro", StringComparison.OrdinalIgnoreCase))
        {
            card.ImageUrl = AppSettings.WaffarXSettings.BaseImgUrl + "img/mezaimg1.png";
        }
        else if (string.Equals(cardType, "visa", StringComparison.OrdinalIgnoreCase))
        {
            card.ImageUrl = AppSettings.WaffarXSettings.BaseImgUrl + "img/visaimg1.png";
        }
    }
    private async Task<bool> CheckIsCibUser()
    {
        try
        {
            return await _instoreRepository.IsUserBankMember(UserId ?? 0, (int)BankEnum.CIB, DateTime.Now);
        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<List<InStoreOffersCardTokenModel>> GetUserCardsWithTypes()
    {
        try
        {
            var cards = await _instoreRepository.GetActiveUserCards(UserId ?? 0);
            if (cards != null && cards.Count > 0)
            {
                foreach (var card in cards)
                {
                    int cardType = await _instoreRepository.GetCardTypeByBin((int)BankEnum.CIB, card.MaskedPan);
                    card.CardTypeId = cardType > 0 ? cardType : card.CardTypeId;
                    card.SubTypeMaskedPan = card.SubTypeMaskedPan + " " +
                        (card.CardTypeId == CREDIT_CARD_TYPE ? "(CreditCard)" : "(DebitCard)");
                }
            }
            return cards;
        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<InStoreStoresModel> BaseGetInstoreStoreById(Guid storeId)
    {
        try
        {
            var result = await _instoreRepository.GetInStoreStoreById(storeId, UserId ?? 0);
            if (result == null)
            {
                return null;
            }
            List<string> ResourceKeys = new List<string>() { "WasText", "storeDisabledAccount", "AddReceipt", "Link" };
            var ResourceList = await _resourceService.GetResourcesList(ResourceKeys);

            string disabledMsg = "";
            var disabledInStoreStores = await _instoreRepository.UserInStoreDisabledStores(UserId ?? 0);
            if (disabledInStoreStores.Count > 0)
            {
                disabledMsg = ResourceList["storeDisabledAccount"];
            }
            var updatedResults = await _bonusService.CalculatesInstoreBonus(new List<InStoreStoresModel> { result });
            var updatedResult = updatedResults.FirstOrDefault(x => x.Id == result.Id);
            result.UpTo = updatedResult.UpTo;
            result.Was = updatedResult.Was;
            result.IsHaveWas = updatedResult.IsHaveWas;
            result.UserCommission = updatedResult.UserCommission;
            result.UserPercentage = updatedResult.UserPercentage;
            result.UpToText = updatedResult.UpToText;
            result.WasText = updatedResult.WasText;
            if (result.UserPercentage == 100)
            {
                result.IsHaveWas = true;
                if (result.Id != (int)SpecialStoresEnum.Amplifon)
                {
                    result.Was = updatedResult.UpTo / 2;
                }
                if (result.IsFlat == true)
                {
                    result.WasText = ResourceList["WasText"] + " " + (!string.IsNullOrEmpty(result.Currency) ? result.Currency : "$") + result.Was.ToString("N2");
                }
                else
                {
                    result.WasText = ResourceList["WasText"] + " " + result.Was.ToString("N2") + "% ";
                }
            }
            result.UserDisabledStore = (disabledInStoreStores.Any(x => x == result.Id)) ? true : false;
            result.UserDisabledStoreMsg = disabledMsg;
            result.Advertisername = (IsEnglish ? result.Advertisername : result.AdvertisernameAr);
            result.WaffarXLogo = !string.IsNullOrEmpty(result.WaffarXLogo) ? (AppSettings.WaffarXSettings.BaseImgUrl + StaticValues.StoreLogos + result.WaffarXLogo).ToLower() : AppSettings.WaffarXSettings.ErrorPage;

            if (result.Id == (int)SpecialStoresEnum.Gas)
            {
                if (result.UserDisabledStore == false)
                {
                    var GasAvailability = await UserGasOrderCountRemaining();
                    if (GasAvailability != null && GasAvailability.Allowed == false)
                    {
                        result.UserDisabledStore = true;
                        result.UserDisabledStoreMsg = GasAvailability.ErrorMsg;
                    }
                }
            }
            result.ShopButtonResource = result.IsLinked ? ResourceList["AddReceipt"] : ResourceList["Link"];
            return result;
        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<List<InStoreStoresModel>> setInstoreStoresModel(List<InStoreStoresModel> model)
    {
        List<string> ResourceKeys = new List<string>() { "WasText", "storeDisabledAccount", "AddReceipt", "Link" };
        var ResourceList = await _resourceService.GetResourcesList(ResourceKeys);

        string DisabledMsg = "";
        var DisabledInStoreStores = await _instoreRepository.UserInStoreDisabledStores(UserId ?? 0);
        if (DisabledInStoreStores.Count > 0)
            DisabledMsg = ResourceList["storeDisabledAccount"];

        var updatedStores = await _bonusService.CalculatesInstoreBonus(model);

        var GasAvailability = await UserGasOrderCountRemaining();

        foreach (InStoreStoresModel store in model)
        {
            var Updatedstore = updatedStores.Where(s => s.Id == store.Id).FirstOrDefault();
            store.UpTo = Updatedstore.UpTo;
            store.Was = Updatedstore.Was;
            store.IsHaveWas = Updatedstore.IsHaveWas;
            store.UserCommission = Updatedstore.UserCommission;
            store.UserPercentage = Updatedstore.UserPercentage;
            store.UpToText = (!string.IsNullOrEmpty(Updatedstore.UpToText) && Updatedstore.UpToText.ToLower().Contains("up")) ? Updatedstore.UpToText.Replace(" cash back", "") : Updatedstore.UpToText;
            store.WasText = Updatedstore.WasText;
            if (store.UserPercentage == 100)
            {
                store.IsHaveWas = true;
                if (store.Id != (int)SpecialStoresEnum.Amplifon)
                    store.Was = Updatedstore.UpTo / 2;

                if (store.IsFlat == true)
                    store.WasText = ResourceList["WasText"] + " " + (!string.IsNullOrEmpty(store.Currency) ? store.Currency : "$") + store.Was.ToSimplifiedString();
                else
                    store.WasText = ResourceList["WasText"] + " " + store.Was.ToSimplifiedString() + "% ";
            }

            store.IsNew = false;
            store.Advertisername = (IsEnglish ? store.Advertisername : store.AdvertisernameAr);
            store.Terms = (IsEnglish ? store.Terms : store.TermsAr);
            store.Description = IsEnglish ? store.Description : store.DescriptionAr;
            store.UserWarning = IsEnglish ? store.UserWarning : store.UserWarningAr;
            store.UserDisabledStore = (DisabledInStoreStores.Any(x => x == store.Id)) ? true : false;
            store.UserDisabledStoreMsg = DisabledMsg;

            if (store.Id == (int)SpecialStoresEnum.Gas && !store.UserDisabledStore && GasAvailability?.Allowed == false)
            {
                store.UserDisabledStore = true;
                store.UserDisabledStoreMsg = GasAvailability.ErrorMsg;
            }
            store.ShopButtonResource = store.IsLinked ? ResourceList["AddReceipt"] : ResourceList["Link"];
        }
        return model;
    }
    private async Task<List<FiltersDataDTO>>  setAllStorePageFilters ()
    {
        List<FiltersDataDTO> filters = new List<FiltersDataDTO>();
        List<int> baseSortTypeIds = new List<int> { (int)InstoreSortFilterEnum.PopularFilter, (int)InstoreSortFilterEnum.NewlyAddedFilter, (int)InstoreSortFilterEnum.CashBack, (int)InstoreSortFilterEnum.AlphabeticalAZFilter, (int)InstoreSortFilterEnum.AlphabeticalZAFilter };
        List<string> baseSortTypeTexts = new List<string> { "PopularFilter", "NewlyAddedFilter", "Cashback", "AlphabeticalAZFilter", "AlphabeticalZAFilter" };

        // Get all resources in a single database call
        var resourceDictionary = await _resourceService.GetResourcesList(baseSortTypeTexts);

        // Create filters using the retrieved resources
        for (int i = 0; i < baseSortTypeIds.Count(); i++)
        {
            string key = baseSortTypeTexts[i];
            string resourceText = resourceDictionary.ContainsKey(key) ? resourceDictionary[key] : key;

            FiltersDataDTO filter = new FiltersDataDTO()
            {
                SortTypeId = baseSortTypeIds[i],
                SortTypeText = resourceText
            };
            filters.Add(filter);
        }

        return filters;
    }

}
