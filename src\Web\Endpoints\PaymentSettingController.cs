using Microsoft.AspNetCore.Mvc;
using WaffarxWebAPIs.Application.Common.Factories;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.CheckUserCashoutCode;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.AmazonGiftCardPayment;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormNoneEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.CIBBankTransfer;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.CreditCardFormEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.Fawry;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.ItalyBank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.KSABank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.MorocoBank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.Wallet;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.SendCashoutCode;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.PaymentSetting;
using WaffarxWebAPIs.Application.Interfaces;
using WaffarxWebAPIs.Application.ServiceInterface.SharedInterfaces;
using WaffarxWebAPIs.Domain.RepositoryInterface.SharedInterfaces;

namespace WaffarxWebAPIs.Web.Endpoints;

[ApiController]
[Route("api/[controller]")]
public class PaymentSettingController : ControllerBase
{
    private readonly IPaymentSettingService _paymentSettingService;
    private readonly IHttpContextAccessor _httpContextAccessor;
    private readonly IResourceService _resourceService;
    private readonly ISharedRepository _sharedRepository;
    private readonly IWaffarXService _waffarXService;

    public PaymentSettingController(IPaymentSettingService paymentSettingService,
                                    IHttpContextAccessor httpContextAccessor,
                                    IResourceService resourceService,
                                    ISharedRepository sharedRepository,
                                    IWaffarXService waffarXService)
    {
        _paymentSettingService = paymentSettingService;
        _httpContextAccessor = httpContextAccessor;
        _resourceService = resourceService;
        _sharedRepository = sharedRepository;
        _waffarXService = waffarXService;
    }

    [HttpGet("GetBanks")]
    public async Task<IActionResult> GetBanks()
    {
        return Ok(await _paymentSettingService.GetBanks());
    }
    [HttpPost("GetBankBranches")]
    public async Task<IActionResult> GetBankBranches(GetBankBranchDto model)
    {
        return Ok(await _paymentSettingService.GetBankBranches(model.BankId));
    }
    [HttpGet("GetCibUserMethod")]
    public async Task<IActionResult> GetCibUserMethod()
    {
        return Ok(await _paymentSettingService.GetCibUserMethod());
    }
    [HttpPost("GetPaymentMethods")]
    public async Task<IActionResult> GetPaymentMethods(GetPaymentMethodDto model)
    {
        return Ok(await _paymentSettingService.GetPaymentMethods(model.CountryId));
    }
    [HttpGet("GetWallets")]
    public async Task<IActionResult> GetWallets()
    {
        return Ok(await _paymentSettingService.GetWallets());
    }
    [HttpPost("GetAgcodStoresByCountry")]
    public async Task<IActionResult> GetAgcodStoresByCountry(GetPaymentMethodDto model)
    {
        return Ok(await _paymentSettingService.GetAgcodStoresByCountry(model.CountryId));
    }
    [HttpGet("GetCashoutCountries")]
    public async Task<IActionResult> GetCashoutCountries()
    {
        return Ok(await _paymentSettingService.GetCashoutCountries());
    }

    [HttpPost("BankFormEGUpdate")]
    public IActionResult BankFormEGUpdate(BankFormEGDto model)
    {
        return ProcessPaymentMethodUpdate(model);
    }

    [HttpPost("FawryUpdate")]
    public IActionResult FawryUpdate(FawryDto model)
    {
        return ProcessPaymentMethodUpdate(model);
    }

    [HttpPost("WalletUpdate")]
    public IActionResult WalletUpdate(WalletDataDto model)
    {
        return ProcessPaymentMethodUpdate(model);
    }

    [HttpPost("BankFormNoneEGUpdate")]
    public IActionResult BankFormNoneEGUpdate(BankFormNoneEGDto model)
    {
        return ProcessPaymentMethodUpdate(model);
    }

    [HttpPost("MoroccoBankUpdate")]
    public IActionResult MoroccoBankUpdate(MorocoBankDto model)
    {
        return ProcessPaymentMethodUpdate(model);
    }

    [HttpPost("ItalyBankUpdate")]
    public IActionResult ItalyBankUpdate(ItalyBankDto model)
    {
        return ProcessPaymentMethodUpdate(model);
    }

    [HttpPost("KSABankUpdate")]
    public IActionResult KSABankUpdate(KSABankDto model)
    {
        return ProcessPaymentMethodUpdate(model);
    }

    [HttpPost("CreditCardUpdate")]
    public IActionResult CreditCardUpdate(CreditCardFormEGDto model)
    {
        return ProcessPaymentMethodUpdate(model);
    }

    [HttpPost("AmazonGiftCardUpdate")]
    public IActionResult AmazonGiftCardUpdate(AmazonGiftCardPaymentDto model)
    {
        return ProcessPaymentMethodUpdate(model);
    }

    [HttpPost("CIBBankTransferUpdate")]
    public IActionResult CIBBankTransferUpdate(CIBBankTransferDto model)
    {
        return ProcessPaymentMethodUpdate(model);
    }

    [HttpPost("SendCashoutMobileCode")]
    public async Task<IActionResult> SendCashoutCode(SendCashoutCodeDto model)
    {
        return Ok(await _paymentSettingService.SendCashoutCode(model));
    }
    [HttpPost("CheckUserCashoutCode")]
    public async Task<IActionResult> CheckUserCashoutCode(CheckUserCashoutCodeDto model)
    {
        return Ok(await _paymentSettingService.CheckUserCashoutCode(model.Code, model.MethodId));
    }

    /// <summary>
    /// Generic method to process all payment method updates using the strategy pattern
    /// </summary>
    private IActionResult ProcessPaymentMethodUpdate<T>(T viewModel) where T : BasePaymentMethodDto
    {
        // Get the appropriate strategy for this view model
        IPaymentMethodStrategy strategy = PaymentMethodStrategyFactory.GetStrategy(
                   viewModel,
                   _httpContextAccessor,
                   _resourceService,
                   _sharedRepository,
                   _waffarXService);

        // Execute the strategy
        return Ok(strategy.UpdatePaymentInfo(_paymentSettingService, viewModel));

    }

}
