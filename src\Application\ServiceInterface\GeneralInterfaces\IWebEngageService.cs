﻿using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Account;
using WaffarxWebAPIs.Application.Common.Models.WebEngageModels;
using WaffarxWebAPIs.Domain.Models.UserModel;
using WaffarxWebAPIs.Domain.Models.UsersModels;

namespace WaffarxWebAPIs.Application.ServiceInterface.GeneralInterfaces;
public interface IWebEngageService
{
    Task<int> SendUserDataToWebEngage(AppUsersModel User, UserRegisterDto UserData, string hashedMobile);
    Task<int> UpdateUserWebEngageData(AppUsersModel User, UserRegisterDto UserData, string hashedMobile);
    int UpdateUserMainDataWebEngage(AppUserModel user);
    int UpdateUserMobileAndSubscribtionWebEngage(int userId, int SourceId, string hashedMobile = "", bool IsSubscribed = false);
    Task<WE_Users_Response> UpdateUserState(int userId);
    Task<WEDeleteResponse> ErasureUserData(int userId);
    Task SendWebEngageEvent(WE_EventModel eventModel);
}
