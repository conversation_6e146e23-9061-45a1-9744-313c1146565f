﻿using Microsoft.AspNetCore.Http;
using WaffarxWebAPIs.Application.Common.Factories;
using WaffarxWebAPIs.Application.Common.Models;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.AmazonGiftCardPayment;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormNoneEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.CIBBankTransfer;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.CreditCardFormEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.Fawry;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.ItalyBank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.KSABank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.MorocoBank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.Wallet;
using WaffarxWebAPIs.Application.Implementations;
using WaffarxWebAPIs.Application.Interfaces;
using WaffarxWebAPIs.Application.ServiceInterface.SharedInterfaces;
using WaffarxWebAPIs.Domain.Enums;
using WaffarxWebAPIs.Domain.RepositoryInterface.SharedInterfaces;

namespace WaffarxWebAPIs.Application.Common.StrategyImplenemtion
{
    public abstract class BasePaymentStrategy : BaseService, IPaymentMethodStrategy
    {
        protected readonly IResourceService _resourceService;
        protected readonly ISharedRepository _sharedRepository;
        protected readonly IWaffarXService _waffarXService;

        protected BasePaymentStrategy(
            IHttpContextAccessor httpContextAccessor,
            IResourceService resourceService,
            ISharedRepository sharedRepository,
            IWaffarXService waffarXService) : base(httpContextAccessor)
        {
            _resourceService = resourceService;
            _sharedRepository = sharedRepository;
            _waffarXService = waffarXService;
        }

        public async Task<GenericResponse<bool>> UpdatePaymentInfo(IPaymentSettingService settingsService, object viewModel)
        {
            if (settingsService == null)
                throw new ArgumentNullException(nameof(settingsService));

            if (viewModel == null)
                throw new ArgumentNullException(nameof(viewModel));

            // Common verification and validation logic
            bool isVerified = await _sharedRepository.IsVerifiedUser(UserId ?? 0);
            var userBank = _waffarXService.BankUserData().BankId;

            if (userBank != (int)BankEnum.CIB)
            {
                return new GenericResponse<bool>
                {
                    Data = false,
                    Errors = [await _resourceService.GetResourceByKey("ErrorHappendWhileSaving")]
                };
            }

            if (!isVerified)
            {
                return new GenericResponse<bool>
                {
                    Data = false,
                    Errors = [await _resourceService.GetResourceByKey("VerificationNotSet")]
                };
            }

            return await ExecutePaymentUpdate(settingsService, viewModel);
        }

        protected abstract Task<GenericResponse<bool>> ExecutePaymentUpdate(IPaymentSettingService settingsService, object viewModel);
    }

    // Concrete implementations - simplified without unnecessary validation
    public class BankFormEGStrategy : BasePaymentStrategy
    {
        public BankFormEGStrategy(IHttpContextAccessor httpContextAccessor, IResourceService resourceService, ISharedRepository sharedRepository, IWaffarXService waffarXService)
            : base(httpContextAccessor, resourceService, sharedRepository, waffarXService) { }

        protected override async Task<GenericResponse<bool>> ExecutePaymentUpdate(IPaymentSettingService settingsService, object viewModel)
        {
            var model = (BankFormEGDto)viewModel;
            var updateResult = await settingsService.BankFormEGUpdate(model);
            return new GenericResponse<bool> { Data = updateResult };
        }
    }

    public class FawryStrategy : BasePaymentStrategy
    {
        public FawryStrategy(IHttpContextAccessor httpContextAccessor, IResourceService resourceService, ISharedRepository sharedRepository, IWaffarXService waffarXService)
            : base(httpContextAccessor, resourceService, sharedRepository, waffarXService) { }

        protected override async Task<GenericResponse<bool>> ExecutePaymentUpdate(IPaymentSettingService settingsService, object viewModel)
        {
            var model = (FawryDto)viewModel;
            var lastVerificationToGet = await _sharedRepository.GetLastVerificationByUserId(UserId ?? 0);
            var mobileValidationResult =  await settingsService.ValidateUserMobile(model.FawryNumber, lastVerificationToGet.PhoneNumber);
            if (!mobileValidationResult.Data)
            {
                return new GenericResponse<bool>
                {
                    Data = false,
                    Errors = [await _resourceService.GetResourceByKey("updatePhoneNumConfimation")]
                };
            }
            var updateResult = await settingsService.UpdateFawry(model);
            return new GenericResponse<bool> { Data = updateResult };
        }
    }

    public class WalletStrategy : BasePaymentStrategy
    {
        public WalletStrategy(IHttpContextAccessor httpContextAccessor, IResourceService resourceService, ISharedRepository sharedRepository, IWaffarXService waffarXService)
            : base(httpContextAccessor, resourceService, sharedRepository, waffarXService) { }

        protected override async Task<GenericResponse<bool>> ExecutePaymentUpdate(IPaymentSettingService settingsService, object viewModel)
        {
            var model = (WalletDataDto)viewModel;
            var lastVerificationToGet = await _sharedRepository.GetLastVerificationByUserId(UserId ?? 0);
            var mobileValidationResult = await settingsService.ValidateUserMobile(model.WalletNumber, lastVerificationToGet.PhoneNumber);
            if (!mobileValidationResult.Data)
            {
                return new GenericResponse<bool>
                {
                    Data = false,
                    Errors = [await _resourceService.GetResourceByKey("updatePhoneNumConfimation")]
                };
            }
            var updateResult = await settingsService.UpdateWallet(model);
            return new GenericResponse<bool> { Data = updateResult };
        }
    }

    public class BankFormNoneEGStrategy : BasePaymentStrategy
    {
        public BankFormNoneEGStrategy(IHttpContextAccessor httpContextAccessor, IResourceService resourceService, ISharedRepository sharedRepository, IWaffarXService waffarXService)
            : base(httpContextAccessor, resourceService, sharedRepository, waffarXService) { }

        protected override async Task<GenericResponse<bool>> ExecutePaymentUpdate(IPaymentSettingService settingsService, object viewModel)
        {
            var model = (BankFormNoneEGDto)viewModel;
            var updateResult = await settingsService.UpdatePaymentInfo(model);
            return new GenericResponse<bool> { Data = updateResult };
        }
    }

    public class MoroccoBankStrategy : BasePaymentStrategy
    {
        public MoroccoBankStrategy(IHttpContextAccessor httpContextAccessor, IResourceService resourceService, ISharedRepository sharedRepository, IWaffarXService waffarXService)
            : base(httpContextAccessor, resourceService, sharedRepository, waffarXService) { }

        protected override async Task<GenericResponse<bool>> ExecutePaymentUpdate(IPaymentSettingService settingsService, object viewModel)
        {
            var model = (MorocoBankDto)viewModel;
            var updateResult = await settingsService.UpdatePaymentInfo(model);
            return new GenericResponse<bool> { Data = updateResult };
        }
    }

    public class ItalyBankStrategy : BasePaymentStrategy
    {
        public ItalyBankStrategy(IHttpContextAccessor httpContextAccessor, IResourceService resourceService, ISharedRepository sharedRepository, IWaffarXService waffarXService)
            : base(httpContextAccessor, resourceService, sharedRepository, waffarXService) { }

        protected override async Task<GenericResponse<bool>> ExecutePaymentUpdate(IPaymentSettingService settingsService, object viewModel)
        {
            var model = (ItalyBankDto)viewModel;
            var updateResult = await settingsService.UpdateItalyUserPaymentInfo(model);
            return new GenericResponse<bool> { Data = updateResult };
        }
    }

    public class KSABankStrategy : BasePaymentStrategy
    {
        public KSABankStrategy(IHttpContextAccessor httpContextAccessor, IResourceService resourceService, ISharedRepository sharedRepository, IWaffarXService waffarXService)
            : base(httpContextAccessor, resourceService, sharedRepository, waffarXService) { }

        protected override async Task<GenericResponse<bool>> ExecutePaymentUpdate(IPaymentSettingService settingsService, object viewModel)
        {
            var model = (KSABankDto)viewModel;
            var updateResult = await settingsService.UpdatePaymentInfo(model);
            return new GenericResponse<bool> { Data = updateResult };
        }
    }

    public class CreditCardEGStrategy : BasePaymentStrategy
    {
        public CreditCardEGStrategy(IHttpContextAccessor httpContextAccessor, IResourceService resourceService, ISharedRepository sharedRepository, IWaffarXService waffarXService)
            : base(httpContextAccessor, resourceService, sharedRepository, waffarXService) { }

        protected override async Task<GenericResponse<bool>> ExecutePaymentUpdate(IPaymentSettingService settingsService, object viewModel)
        {
            var model = (CreditCardFormEGDto)viewModel;
            var updateResult = await settingsService.CreditCardFormEGUpdate(model);
            return new GenericResponse<bool> { Data = updateResult };
        }
    }

    public class AGCODStrategy : BasePaymentStrategy
    {
        public AGCODStrategy(IHttpContextAccessor httpContextAccessor, IResourceService resourceService, ISharedRepository sharedRepository, IWaffarXService waffarXService)
            : base(httpContextAccessor, resourceService, sharedRepository, waffarXService) { }

        protected override async Task<GenericResponse<bool>> ExecutePaymentUpdate(IPaymentSettingService settingsService, object viewModel)
        {
            var model = (AmazonGiftCardPaymentDto)viewModel;
            var updateResult = await settingsService.AmazonGiftCardFormUpdate(model);
            return new GenericResponse<bool> { Data = updateResult };
        }
    }

    public class CIBBankTransferStrategy : BasePaymentStrategy
    {
        public CIBBankTransferStrategy(IHttpContextAccessor httpContextAccessor, IResourceService resourceService, ISharedRepository sharedRepository, IWaffarXService waffarXService)
            : base(httpContextAccessor, resourceService, sharedRepository, waffarXService) { }

        protected override async Task<GenericResponse<bool>> ExecutePaymentUpdate(IPaymentSettingService settingsService, object viewModel)
        {
            var model = (CIBBankTransferDto)viewModel;
            if (!CheckIfValidAccountNumber(model.CibAccountNumber))
            {
                return new GenericResponse<bool>
                {
                    Data = false,
                    Errors = [await _resourceService.GetResourceByKey("accountMustBegin1000")]
                };
            }
            var updateResult = await settingsService.CibBankTransferUpdate(model);
            return new GenericResponse<bool> { Data = updateResult };
        }
        private bool CheckIfValidAccountNumber(string accountNumber)
        {
            bool result = false;
            try
            {
                string bintext = accountNumber.Substring(0, 4);
                if (bintext == "1000")
                {
                    result = true;
                }
            }
            catch (Exception)
            {
                throw
            }
            return result;
        }
    }
}
