﻿using Microsoft.AspNetCore.Http;
using WaffarxWebAPIs.Application.Common.Models;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.AmazonGiftCardPayment;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormNoneEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.CIBBankTransfer;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.CreditCardFormEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.Fawry;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.ItalyBank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.KSABank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.MorocoBank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.Wallet;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.SendCashoutCode;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Cashout;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.InstoreModels;
using WaffarxWebAPIs.Application.Common.Models.WebEngageModels;
using WaffarxWebAPIs.Application.Implementations;
using WaffarxWebAPIs.Application.Interfaces;
using WaffarxWebAPIs.Application.ServiceInterface.GeneralInterfaces;
using WaffarxWebAPIs.Application.ServiceInterface.SharedInterfaces;
using WaffarxWebAPIs.Domain.Constants;
using WaffarxWebAPIs.Domain.Entities;
using WaffarxWebAPIs.Domain.Enums;
using WaffarxWebAPIs.Domain.Interfaces.SharedInterfaces;
using WaffarxWebAPIs.Domain.Models.SharedModels;
using WaffarxWebAPIs.Domain.RepositoryInterface.SharedInterfaces;

namespace WaffarxWebAPIs.Application.ServiceImplementation.SharedServices;
public class PaymentSettingService : BaseService, IPaymentSettingService
{
    private readonly IPaymentSettingRepository _paymentSettingRepository;
    private readonly ISharedRepository _sharedRepository;
    private readonly IInstoreRepository _instoreRepository;
    private readonly IResourceService _resourceService;
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;
    private readonly IWaffarXService _waffarxService;
    private readonly IWebEngageService _webEngageService;
    private readonly ICashoutRepository _cashoutRepository;

    public PaymentSettingService(IPaymentSettingRepository paymentSettingRepository, IMapper mapper, IResourceService resourceService,
                                 ISharedRepository sharedRepository, IInstoreRepository instoreRepository, IUserRepository userRepository,
                                 IHttpContextAccessor httpContextAccessor, IWaffarXService waffarxService, IWebEngageService webEngageService, ICashoutRepository cashoutRepository)
        : base(httpContextAccessor)
    {
        _paymentSettingRepository = paymentSettingRepository;
        _mapper = mapper;
        _resourceService = resourceService;
        _sharedRepository = sharedRepository;
        _instoreRepository = instoreRepository;
        _userRepository = userRepository;
        _waffarxService = waffarxService;
        _webEngageService = webEngageService;
        _cashoutRepository = cashoutRepository;
    }

    public async Task<GenericResponse<AmazonGiftCardDto>> GetAgcodStoresByCountry(int countryId)
    {
        try
        {

            var stores = new Dictionary<int, string>
                 {
                     { (int) SpecialStoresEnum.AmazonSA, "AmazonKSAGiftCard" },
                     { (int) SpecialStoresEnum.AmazonAE, "AmazonUAEGiftCard" },
                     { (int) SpecialStoresEnum.AmazonEg, "AmazonEGPGiftCard" }
                 };

            var resourceList = await _resourceService.GetResourcesList(stores.Values.ToList());

            if (countryId == (int)CountryEnum.Egypt)
            {
                stores = new Dictionary<int, string> { { (int)SpecialStoresEnum.AmazonEg, "AmazonEGPGiftCard" } };
            }

            var storeTypes = stores.Select(s => new AmazoneStoreTypeDto { Id = s.Key, Name = resourceList.FirstOrDefault(r => r.Key == s.Value).Value }).ToList();
            return new GenericResponse<AmazonGiftCardDto>
            {
                Status = StaticValues.Success,
                Data = new AmazonGiftCardDto
                {
                    AgcodStores = storeTypes,
                    AGCODNote = await _resourceService.GetResourceByKey("AGCODSettingsNote")
                }
            };
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<GenericResponse<List<BankBranchDto>>> GetBankBranches(int bankId)
    {
        try
        {
            var banksbranchesToGet = await _paymentSettingRepository.GetBankBranches(bankId, IsEnglish);
            if (banksbranchesToGet == null)
            {
                return new GenericResponse<List<BankBranchDto>>
                {
                    Status = StaticValues.Error,
                    Data = new List<BankBranchDto>()
                };
            }
            return new GenericResponse<List<BankBranchDto>>
            {
                Status = StaticValues.Success,
                Data = _mapper.Map<List<BankBranchDto>>(banksbranchesToGet)
            };
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<GenericResponse<List<BanksDto>>> GetBanks()
    {
        try
        {
            var banksToGet = await _paymentSettingRepository.GetBanks(IsEnglish);
            if (banksToGet == null)
            {
                return new GenericResponse<List<BanksDto>>
                {
                    Status = StaticValues.Error,
                    Data = new List<BanksDto>()
                };
            }
            return new GenericResponse<List<BanksDto>>
            {
                Status = StaticValues.Success,
                Data = _mapper.Map<List<BanksDto>>(banksToGet)
            };
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<GenericResponse<CibCashoutMethodDto>> GetCibUserMethod()
    {
        try
        {
            var cardsToGet = await _instoreRepository.GetActiveUserCards(UserId ?? 0, true);
            if (cardsToGet == null)
            {
                return new GenericResponse<CibCashoutMethodDto>
                {
                    Status = StaticValues.Error,
                    Data = new CibCashoutMethodDto()
                };

            }
            CibCashoutMethodDto cibCashoutMethod = new CibCashoutMethodDto();
            var userPaymentInfo = await _userRepository.GetUserPaymentInfo(UserId ?? 0, IsEnglish);
            Dictionary<string, int> types = await _instoreRepository.GetCardTypesByBin((int)BankEnum.CIB, cardsToGet.Select(x => x.MaskedPan).ToList());
            cardsToGet.ForEach(c =>
            {
                c.CardTypeId = types[c.MaskedPan];
                c.SubTypeMaskedPan = c.SubTypeMaskedPan + " " + (c.CardTypeId == 1 ? "(CreditCard)" : "(DebitCard)");
                c.CanBeDeleted = true;
                if (userPaymentInfo.PaymentMethodId == (int)PaymentMethodEnum.CIBCard)
                {
                    if (c.MaskedPan == userPaymentInfo.Ccmasked)
                    {
                        c.CanBeDeleted = false;
                    }
                }
            });
            cibCashoutMethod.IsDebitSelected = false;
            if (userPaymentInfo.PaymentMethodId == (int)PaymentMethodEnum.CIBBankTransfer)
            {
                cibCashoutMethod.IsDebitSelected = true;

            }
            else
            {
                var card = cardsToGet.Where(x => x.CanBeDeleted == false).FirstOrDefault();
                if (card != null)
                {
                    cardsToGet.Where(x => x.CanBeDeleted == false).FirstOrDefault().SelectedCard = true;
                }
            }
            // Is Have Debit To Add BankTransfer
            cibCashoutMethod.IsHaveDebit = false;
            cibCashoutMethod.BankTransferData = new CibBankTransferDto();
            cibCashoutMethod.CreditCards = new List<InStoreOffersCardTokenDto>();
            int debitCardCount = cardsToGet.Where(c => c.CardTypeId == 2).Count();
            if (debitCardCount > 0)
            {
                cibCashoutMethod.IsHaveDebit = true;
                cibCashoutMethod.BankTransferData = new CibBankTransferDto()
                {
                    Name = await _resourceService.GetResourceByKey("CIBBankTransfer"),
                    Icon = ""
                };
            }
            cibCashoutMethod.CreditCards = _mapper.Map<List<InStoreOffersCardTokenDto>>(cardsToGet.Where(c => c.CardTypeId == 1).ToList());

            if (cibCashoutMethod != null && (cibCashoutMethod.CreditCards.Count > 0 || cibCashoutMethod.IsHaveDebit == true))
            {
                return new GenericResponse<CibCashoutMethodDto>
                {
                    Data = cibCashoutMethod,
                    Status = StaticValues.Success,
                };
            }
            else
            {
                return new GenericResponse<CibCashoutMethodDto>
                {
                    Data = new CibCashoutMethodDto(),
                    Status = StaticValues.Error,
                };
            }
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<GenericResponse<List<PaymentMethodDto>>> GetPaymentMethods(int countryId)
    {
        try
        {
            var methodIds = countryId switch
            {
                (int)CountryEnum.Egypt => new List<int> { (int)CashoutMethodsEnum.Fawry, (int)CashoutMethodsEnum.Wallets, (int)CashoutMethodsEnum.BankTransfer, (int)CashoutMethodsEnum.CreditCards, (int)CashoutMethodsEnum.AmazonGiftCards },
                (int)CountryEnum.SaudiArabia => new List<int> { (int)CashoutMethodsEnum.BankTransfer, (int)CashoutMethodsEnum.AmazonGiftCards, (int)CashoutMethodsEnum.Mobily },
                _ => new List<int> { (int)CashoutMethodsEnum.BankTransfer, (int)CashoutMethodsEnum.AmazonGiftCards }
            };
            List<PaymentMethodModel> paymentMethod = new List<PaymentMethodModel>();
            if (methodIds.Any())
            {
                paymentMethod = await _sharedRepository.GetPaymentMethodsById(methodIds, countryId, IsEnglish);
            }
            if (!paymentMethod.Any())
            {
                var resourceList = await _resourceService.GetResourcesList(new List<string> { "BankTransfer", "AmazonGiftCard" });
                paymentMethod.AddRange(new List<PaymentMethodModel>
                {
                    new PaymentMethodModel { Id = 1002, Name = resourceList["BankTransfer"] , CountryId = countryId, TypeId = "10022" },
                    new PaymentMethodModel { Id = 1016, Name =  resourceList["AmazonGiftCard"] , CountryId = countryId, TypeId = "1016" }
                });
            }
            return new GenericResponse<List<PaymentMethodDto>>
            {
                Status = StaticValues.Success,
                Data = _mapper.Map<List<PaymentMethodDto>>(paymentMethod)
            };
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<GenericResponse<List<WalletDto>>> GetWallets()
    {
        try
        {
            var walletToGet = await _paymentSettingRepository.GetWallets(IsEnglish);
            if (walletToGet == null)
            {
                return new GenericResponse<List<WalletDto>>
                {
                    Data = new List<WalletDto>(),
                    Status = StaticValues.Error,

                };
            }
            return new GenericResponse<List<WalletDto>>
            {
                Status = StaticValues.Success,
                Data = _mapper.Map<List<WalletDto>>(walletToGet)
            };

        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<CashoutCountriesDto>> GetCashoutCountries()
    {
        CashoutCountriesDto CashoutCountriesData = new CashoutCountriesDto();
        try
        {
            var countriesToGet = await _paymentSettingRepository.GetCountries();
            if (countriesToGet?.Count > 0)
            {
                int UserCashoutCountry = await _userRepository.GetUserPaymentCountry(UserId ?? 0);
                if (UserCashoutCountry > 0)
                {
                    CashoutCountriesData.SelectedCountryId = UserCashoutCountry;
                    CashoutCountriesData.Countries = _mapper.Map<List<CountryDto>>(countriesToGet);
                }
                else
                {
                    CashoutCountriesData.SelectedCountryId = 0;
                    List<int> MainCountriesIds = new List<int>() { 67, 195, 2, 125 };

                    var CountriesList = _mapper.Map<List<CountryDto>>(countriesToGet);
                    var MainCountriesList = CountriesList.Where(c => MainCountriesIds.Contains(c.Id)).ToList();

                    CountriesList.RemoveAll(x => MainCountriesIds.Contains(x.Id));
                    MainCountriesList.AddRange(CountriesList);
                    CashoutCountriesData.Countries = MainCountriesList;
                }
                return new GenericResponse<CashoutCountriesDto>
                {
                    Status = StaticValues.Success,
                    Data = CashoutCountriesData
                };

            }
            return new GenericResponse<CashoutCountriesDto>
            {
                Status = StaticValues.Error,
                Data = new CashoutCountriesDto() { Countries = new List<CountryDto>(), SelectedCountryId = 0 }
            };

        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<VerificationStatusDto>> SendCashoutCode(SendCashoutCodeDto model)
    {
        try
        {
            var response = new GenericResponse<VerificationStatusDto>();
            int result = 0;

            // Get status
            var statusmodel = await _cashoutRepository.GetCashoutCodeStatus(UserId ?? 0, model.UserMobileNumber);

            if (statusmodel.IsSentBefore)
            {
                result = 2;
            }
            else
            {
                result = await _cashoutRepository.SetUserCashoutCode(model.UserMobileNumber, model.CountryCode, model.SendMethodId, UserId ?? 0);
                if (result == 1)
                {
                    statusmodel.PhoneNumber = model.UserMobileNumber;
                }
            }

            // Set notices

            statusmodel.Notice = "";
            statusmodel.NoticeWithPhone = await _resourceService.GetResourceByKey("ConfirmationCodeSentAlready2") + " " + (statusmodel.PhoneNumber ?? "");
            statusmodel.CustomerServiceNotice = await _resourceService.GetResourceByKey("ReviewWithCustomerServiceAA");
            statusmodel.ContactCustomerServiceNotice = await _resourceService.GetResourceByKey("ReviewWithCustomerServiceSS");

            var statusDto = _mapper.Map<VerificationStatusDto>(statusmodel);
            // Handle result
            switch (result)
            {
                case 0:
                    response.Data = statusDto;
                    response.Message = await _resourceService.GetResourceByKey("CheckPhoneNumberAndResend");
                    break;
                case 1:
                    statusDto.CountryCode = model.CountryCode;
                    response.Data = statusDto;
                    response.Message = await _resourceService.GetResourceByKey("CodeSent2");
                    break;
                case 2:
                    statusDto.CountryCode = model.CountryCode;
                    response.Data = statusDto;
                    response.Message = await _resourceService.GetResourceByKey("ForgetPasswordCodeAlreadySent");
                    break;
                case -1:
                    response.Data = statusDto;
                    response.Message = model.CountryCode == 966
                        ? await _resourceService.GetResourceByKey("InvalidNumber")
                        : await _resourceService.GetResourceByKey("CheckPhoneNumberValidity");
                    break;
                case -2:
                    response.Data = statusDto;
                    response.Message = await _resourceService.GetResourceByKey("ExceedResetConfirmation");
                    break;
                default:
                    response.Data = statusDto;
                    response.Message = await _resourceService.GetResourceByKey("UnknownError");
                    break;
            }

            return response;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<bool>> CheckUserCashoutCode(string code, int methodId)
    {
        try
        {
            var response = await _cashoutRepository.CheckUserCashoutCode(UserId ?? 0, code, methodId);
            if (response)
            {
                return new GenericResponse<bool>
                {
                    Data = true,
                    Message = await _resourceService.GetResourceByKey("MobileVerified")
                };
            }
            return new GenericResponse<bool>
            {
                Errors = [await _resourceService.GetResourceByKey("pleaseMakeCodeIsCorrect")],
                Data = false
            };
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<bool>> ValidateUserMobile(string mobileNumber, string verifiedUserNumber)
    {
        try
        {
            if (mobileNumber == verifiedUserNumber)
            {
                return new GenericResponse<bool>
                {
                    Data = true,
                };
            }
            var checkResult = await _cashoutRepository.CheckUserCashoutVerifiedNumber(mobileNumber, UserId ?? 0);
            if (checkResult)
            {
                return new GenericResponse<bool>
                {
                    Data = true,
                };
            }
            return new GenericResponse<bool>
            {
                Errors = [await _resourceService.GetResourceByKey("pleaseMakeCodeIsCorrect")],
                Data = false
            };
        }
        catch (Exception)
        {
            throw;
        }
    }
    #region Update Methods
    // Generic method to handle the common pattern
    private async Task<UserPaymentInfo> GetOrCreateUserPaymentInfo()
    {
        var userPaymentInfo = await _paymentSettingRepository.GetUserPaymentInfo(UserId ?? 0);

        if (userPaymentInfo == null)
        {
            userPaymentInfo = new UserPaymentInfo
            {
                UserId = UserId,
                IsActive = true,
                SourceId = 2,
                Cdate = DateTime.Now
            };
        }
        else if (!userPaymentInfo.Cdate.HasValue)
        {
            userPaymentInfo.Cdate = DateTime.Now;
        }

        return userPaymentInfo;
    }

    // Helper method to update bank information
    private async Task UpdateBankInfo(UserPaymentInfo userPaymentInfo, int? bankId, int? branchId)
    {
        if (bankId.HasValue && userPaymentInfo.BankId != bankId)
        {
            userPaymentInfo.BankId = bankId;
            var bank = await _paymentSettingRepository.GetBank(bankId.Value, IsEnglish);
            userPaymentInfo.BankName = bank.BankName;
        }

        if (branchId.HasValue && userPaymentInfo.BranchId != branchId)
        {
            userPaymentInfo.BranchId = branchId;
            var bankBranch = await _paymentSettingRepository.GetBankBranch(branchId.Value, IsEnglish);
            userPaymentInfo.BankName = bankBranch.BranchName; // Note: This overwrites BankName - verify if this is intended
        }
    }

    // Generic method for common operations
    private async Task ExecuteCommonOperations(int countryId, string webEngageEvent = null)
    {
        await _userRepository.UpdateUserMailingCountry(UserId ?? 0, countryId);
        await _waffarxService.UpdateUserCashOutCountry(countryId, 3, UserId ?? 0);

        if (!string.IsNullOrEmpty(webEngageEvent))
        {
            await SendEventToWebEngage(webEngageEvent);
        }
    }

    // Optimized Egypt Bank Form Update
    public async Task<bool> BankFormEGUpdate(BankFormEGDto model)
    {
        const int egyptCountryId = (int)CountryEnum.Egypt;

        var userPaymentInfo = await GetOrCreateUserPaymentInfo();

        // Execute common operations
        await ExecuteCommonOperations(egyptCountryId, "EgyptBank");

        // Update bank information
        await UpdateBankInfo(userPaymentInfo, model.BankId, model.BranchId);

        // Update specific fields
        userPaymentInfo.CountryId = egyptCountryId;
        userPaymentInfo.AccountNumber = model.AccountNumber;
        userPaymentInfo.SwiftCode = model.SwiftCode;
        userPaymentInfo.AccountHolderName = model.AccountHolderName;
        userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.BankTransfer;

        return await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
    }

    // Optimized Fawry Update
    public async Task<bool> UpdateFawry(FawryDto model)
    {
        var userPaymentInfo = await GetOrCreateUserPaymentInfo();

        await ExecuteCommonOperations(model.CountryID, "Fawry");

        userPaymentInfo.CountryId = (int)CountryEnum.Egypt;
        userPaymentInfo.FawryMobile = model.FawryNumber;
        userPaymentInfo.FawryName = model.FawryName;
        userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.Fawry;

        return await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
    }

    // Optimized Wallet Update
    public async Task<bool> UpdateWallet(WalletDataDto model)
    {
        const int egyptCountryId = (int)CountryEnum.Egypt;

        var userPaymentInfo = await GetOrCreateUserPaymentInfo();

        await ExecuteCommonOperations(egyptCountryId, "EWallet");

        userPaymentInfo.CountryId = egyptCountryId;
        userPaymentInfo.WalletId = model.WalletID;
        userPaymentInfo.WalletName = model.WalletName;
        userPaymentInfo.WalletNo = model.WalletNumber;
        userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.EWallets;

        return await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
    }

    // Generic helper for bank transfer updates
    private async Task<bool> UpdateBankTransferInternal<T>(T model, int countryId, string webEngageEvent = null)
        where T : IBankTransferDto
    {
        var userPaymentInfo = await GetOrCreateUserPaymentInfo();

        if (!string.IsNullOrEmpty(webEngageEvent))
        {
            await ExecuteCommonOperations(countryId, webEngageEvent);
        }

        userPaymentInfo.CountryId = countryId;
        userPaymentInfo.BillingAddress = model.BankAddress;
        userPaymentInfo.BankName = model.BankName;
        userPaymentInfo.BankAddress = model.BankAddress;
        userPaymentInfo.BankBranchName = model.BankBranchName;
        userPaymentInfo.AccountNumber = model.AccountNo;
        userPaymentInfo.SwiftCode = model.SWIFTCode;
        userPaymentInfo.Iban = model.IBAN;
        userPaymentInfo.AccountHolderName = model.AccountHolderName;
        userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.BankTransfer;

        // Handle country-specific fields
        if (model is IMoroccoSpecificDto moroccoDto)
        {
            userPaymentInfo.Dob = moroccoDto.DOB.HasValue ? DateOnly.FromDateTime(moroccoDto.DOB.Value) : null;
            userPaymentInfo.FirstName = moroccoDto.FirstName;
            userPaymentInfo.LastName = moroccoDto.LastName;
            userPaymentInfo.MiddleName = moroccoDto.MiddleName;
        }

        if (model is IItalySpecificDto italyDto)
        {
            userPaymentInfo.BankEmail = italyDto.BanKEmail;
            userPaymentInfo.PostalCode = italyDto.PostCode;
            userPaymentInfo.BillingAddress = italyDto.AccountHolderResidentialAddress;
        }

        return await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
    }

    // Keep original method names - Non-Egypt Bank update method
    public async Task<bool> UpdatePaymentInfo(BankFormNoneEGDto model)
    {
        return await UpdateBankTransferInternal(model, CountryId);
    }

    // Keep original method names - Morocco Bank update method (overloaded)
    public async Task<bool> UpdatePaymentInfo(MorocoBankDto model)
    {
        return await UpdateBankTransferInternal(model, (int)CountryEnum.Morocco);
    }

    // Keep original method names - KSA Bank update method (overloaded)
    public async Task<bool> UpdatePaymentInfo(KSABankDto model)
    {
        return await UpdateBankTransferInternal(model, (int)CountryEnum.SaudiArabia);
    }

    // Keep original method names - Italy Bank update method
    public async Task<bool> UpdateItalyUserPaymentInfo(ItalyBankDto model)
    {
        return await UpdateBankTransferInternal(model, (int)CountryEnum.Italy);
    }

    // Optimized Credit Card Update
    public async Task<bool> CreditCardFormEGUpdate(CreditCardFormEGDto model)
    {
        const int egyptCountryId = 67; // Consider using enum value instead

        var userPaymentInfo = await GetOrCreateUserPaymentInfo();

        await UpdateBankInfo(userPaymentInfo, model.BankId, model.BranchId);

        userPaymentInfo.CountryId = egyptCountryId;
        userPaymentInfo.CreditCardNumber = model.CreditCardNo;
        userPaymentInfo.SwiftCode = model.SWIFTCode;
        userPaymentInfo.AccountHolderName = model.AccountHolderName;
        userPaymentInfo.PaymentMethodId = 1013; // Consider using enum

        return await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
    }

    // Optimized Amazon Gift Card Update
    public async Task<bool> AmazonGiftCardFormUpdate(AmazonGiftCardPaymentDto model)
    {
        var userPaymentInfo = await GetOrCreateUserPaymentInfo();

        userPaymentInfo.CountryId = model.CountryId;
        userPaymentInfo.AgcodstoreId = model.GiftCardStoreId;
        userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.AmazonGiftCard;

        return await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
    }
    public async Task<bool> CibBankTransferUpdate(CIBBankTransferDto model)
    {

        //UpdateUserCashOutCountry(67, 3);
        UserPaymentInfo userPaymentInfo = await _paymentSettingRepository.GetUserPaymentInfo(UserId ?? 0);
        if (userPaymentInfo != null)
        {
            userPaymentInfo.CountryId = (int)CountryEnum.Egypt;
            userPaymentInfo.BankId = model.CreditBankID;
            userPaymentInfo.BranchId = model.CreditBankID;
            userPaymentInfo.SwiftCode = model.CreditSWIFTCode;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.CIBBankTransfer;
            userPaymentInfo.IsActive = true;
            userPaymentInfo.SourceId = 2;
            userPaymentInfo.CardTypeId = 2;
            userPaymentInfo.AccountHolderName = model.AccountHolderName;
            userPaymentInfo.CibaccountNumber = model.CibAccountNumber;

            if (!userPaymentInfo.Cdate.HasValue)
            {
                userPaymentInfo.Cdate = DateTime.Now;
            }

        }
        else // missing else
        {

        }
        var result = await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
        return result;
        // UpdateMailingCountry(userPaymentInfo.CountryID.Value);
        //return result;
    }

    private async Task SendEventToWebEngage(string payment)
    {
        await _webEngageService.SendWebEngageEvent(new WE_EventModel
        {
            userId = UserId.Value.ToString(),
            eventName = "HasPayment",
            eventTime = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:sszz00"),
            eventData = new WE_EventDataModel()
            {
                payment_name = payment,
                user_id = UserId.Value
            }

        });
    }

    #endregion
}
