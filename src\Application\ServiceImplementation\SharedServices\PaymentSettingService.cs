using Microsoft.AspNetCore.Http;
using WaffarxWebAPIs.Application.Common.Models;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.AmazonGiftCardPayment;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormNoneEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.CIBBankTransfer;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.CreditCardFormEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.Fawry;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.ItalyBank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.KSABank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.MorocoBank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.Wallet;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.SendCashoutCode;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Cashout;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.InstoreModels;
using WaffarxWebAPIs.Application.Common.Models.WebEngageModels;
using WaffarxWebAPIs.Application.Implementations;
using WaffarxWebAPIs.Application.Interfaces;
using WaffarxWebAPIs.Application.ServiceInterface.GeneralInterfaces;
using WaffarxWebAPIs.Application.ServiceInterface.SharedInterfaces;
using WaffarxWebAPIs.Domain.Constants;
using WaffarxWebAPIs.Domain.Entities;
using WaffarxWebAPIs.Domain.Enums;
using WaffarxWebAPIs.Domain.Interfaces.SharedInterfaces;
using WaffarxWebAPIs.Domain.Models.SharedModels;
using WaffarxWebAPIs.Domain.RepositoryInterface.SharedInterfaces;

namespace WaffarxWebAPIs.Application.ServiceImplementation.SharedServices;
public class PaymentSettingService : BaseService, IPaymentSettingService
{
    private readonly IPaymentSettingRepository _paymentSettingRepository;
    private readonly ISharedRepository _sharedRepository;
    private readonly IInstoreRepository _instoreRepository;
    private readonly IResourceService _resourceService;
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;
    private readonly IWaffarXService _waffarxService;
    private readonly IWebEngageService _webEngageService;
    private readonly ICashoutRepository _cashoutRepository;

    public PaymentSettingService(IPaymentSettingRepository paymentSettingRepository, IMapper mapper, IResourceService resourceService,
                                 ISharedRepository sharedRepository, IInstoreRepository instoreRepository, IUserRepository userRepository,
                                 IHttpContextAccessor httpContextAccessor, IWaffarXService waffarxService, IWebEngageService webEngageService, ICashoutRepository cashoutRepository)
        : base(httpContextAccessor)
    {
        _paymentSettingRepository = paymentSettingRepository;
        _mapper = mapper;
        _resourceService = resourceService;
        _sharedRepository = sharedRepository;
        _instoreRepository = instoreRepository;
        _userRepository = userRepository;
        _waffarxService = waffarxService;
        _webEngageService = webEngageService;
        _cashoutRepository = cashoutRepository;
    }

    public async Task<GenericResponse<AmazonGiftCardDto>> GetAgcodStoresByCountry(int countryId)
    {
        try
        {

            var stores = new Dictionary<int, string>
                 {
                     { (int) SpecialStoresEnum.AmazonSA, "AmazonKSAGiftCard" },
                     { (int) SpecialStoresEnum.AmazonAE, "AmazonUAEGiftCard" },
                     { (int) SpecialStoresEnum.AmazonEg, "AmazonEGPGiftCard" }
                 };

            var resourceList = await _resourceService.GetResourcesList(stores.Values.ToList());

            if (countryId == (int)CountryEnum.Egypt)
            {
                stores = new Dictionary<int, string> { { (int)SpecialStoresEnum.AmazonEg, "AmazonEGPGiftCard" } };
            }

            var storeTypes = stores.Select(s => new AmazoneStoreTypeDto { Id = s.Key, Name = resourceList.FirstOrDefault(r => r.Key == s.Value).Value }).ToList();
            return new GenericResponse<AmazonGiftCardDto>
            {
                Status = StaticValues.Success,
                Data = new AmazonGiftCardDto
                {
                    AgcodStores = storeTypes,
                    AGCODNote = await _resourceService.GetResourceByKey("AGCODSettingsNote")
                }
            };
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<GenericResponse<List<BankBranchDto>>> GetBankBranches(int bankId)
    {
        try
        {
            var banksbranchesToGet = await _paymentSettingRepository.GetBankBranches(bankId, IsEnglish);
            if (banksbranchesToGet == null)
            {
                return new GenericResponse<List<BankBranchDto>>
                {
                    Status = StaticValues.Error,
                    Data = new List<BankBranchDto>()
                };
            }
            return new GenericResponse<List<BankBranchDto>>
            {
                Status = StaticValues.Success,
                Data = _mapper.Map<List<BankBranchDto>>(banksbranchesToGet)
            };
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<GenericResponse<List<BanksDto>>> GetBanks()
    {
        try
        {
            var banksToGet = await _paymentSettingRepository.GetBanks(IsEnglish);
            if (banksToGet == null)
            {
                return new GenericResponse<List<BanksDto>>
                {
                    Status = StaticValues.Error,
                    Data = new List<BanksDto>()
                };
            }
            return new GenericResponse<List<BanksDto>>
            {
                Status = StaticValues.Success,
                Data = _mapper.Map<List<BanksDto>>(banksToGet)
            };
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<GenericResponse<CibCashoutMethodDto>> GetCibUserMethod()
    {
        try
        {
            var cardsToGet = await _instoreRepository.GetActiveUserCards(UserId ?? 0, true);
            if (cardsToGet == null)
            {
                return new GenericResponse<CibCashoutMethodDto>
                {
                    Status = StaticValues.Error,
                    Data = new CibCashoutMethodDto()
                };

            }
            CibCashoutMethodDto cibCashoutMethod = new CibCashoutMethodDto();
            var userPaymentInfo = await _userRepository.GetUserPaymentInfo(UserId ?? 0, IsEnglish);
            Dictionary<string, int> types = await _instoreRepository.GetCardTypesByBin((int)BankEnum.CIB, cardsToGet.Select(x => x.MaskedPan).ToList());
            cardsToGet.ForEach(c =>
            {
                c.CardTypeId = types[c.MaskedPan];
                c.SubTypeMaskedPan = c.SubTypeMaskedPan + " " + (c.CardTypeId == 1 ? "(CreditCard)" : "(DebitCard)");
                c.CanBeDeleted = true;
                if (userPaymentInfo.PaymentMethodId == (int)PaymentMethodEnum.CIBCard)
                {
                    if (c.MaskedPan == userPaymentInfo.Ccmasked)
                    {
                        c.CanBeDeleted = false;
                    }
                }
            });
            cibCashoutMethod.IsDebitSelected = false;
            if (userPaymentInfo.PaymentMethodId == (int)PaymentMethodEnum.CIBBankTransfer)
            {
                cibCashoutMethod.IsDebitSelected = true;

            }
            else
            {
                var card = cardsToGet.Where(x => x.CanBeDeleted == false).FirstOrDefault();
                if (card != null)
                {
                    cardsToGet.Where(x => x.CanBeDeleted == false).FirstOrDefault().SelectedCard = true;
                }
            }
            // Is Have Debit To Add BankTransfer
            cibCashoutMethod.IsHaveDebit = false;
            cibCashoutMethod.BankTransferData = new CibBankTransferDto();
            cibCashoutMethod.CreditCards = new List<InStoreOffersCardTokenDto>();
            int debitCardCount = cardsToGet.Where(c => c.CardTypeId == 2).Count();
            if (debitCardCount > 0)
            {
                cibCashoutMethod.IsHaveDebit = true;
                cibCashoutMethod.BankTransferData = new CibBankTransferDto()
                {
                    Name = await _resourceService.GetResourceByKey("CIBBankTransfer"),
                    Icon = ""
                };
            }
            cibCashoutMethod.CreditCards = _mapper.Map<List<InStoreOffersCardTokenDto>>(cardsToGet.Where(c => c.CardTypeId == 1).ToList());

            if (cibCashoutMethod != null && (cibCashoutMethod.CreditCards.Count > 0 || cibCashoutMethod.IsHaveDebit == true))
            {
                return new GenericResponse<CibCashoutMethodDto>
                {
                    Data = cibCashoutMethod,
                    Status = StaticValues.Success,
                };
            }
            else
            {
                return new GenericResponse<CibCashoutMethodDto>
                {
                    Data = new CibCashoutMethodDto(),
                    Status = StaticValues.Error,
                };
            }
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<GenericResponse<List<PaymentMethodDto>>> GetPaymentMethods(int countryId)
    {
        try
        {
            var methodIds = countryId switch
            {
                (int)CountryEnum.Egypt => new List<int> { (int)CashoutMethodsEnum.Fawry, (int)CashoutMethodsEnum.Wallets, (int)CashoutMethodsEnum.BankTransfer, (int)CashoutMethodsEnum.CreditCards, (int)CashoutMethodsEnum.AmazonGiftCards },
                (int)CountryEnum.SaudiArabia => new List<int> { (int)CashoutMethodsEnum.BankTransfer, (int)CashoutMethodsEnum.AmazonGiftCards, (int)CashoutMethodsEnum.Mobily },
                _ => new List<int> { (int)CashoutMethodsEnum.BankTransfer, (int)CashoutMethodsEnum.AmazonGiftCards }
            };
            List<PaymentMethodModel> paymentMethod = new List<PaymentMethodModel>();
            if (methodIds.Any())
            {
                paymentMethod = await _sharedRepository.GetPaymentMethodsById(methodIds, countryId, IsEnglish);
            }
            if (!paymentMethod.Any())
            {
                var resourceList = await _resourceService.GetResourcesList(new List<string> { "BankTransfer", "AmazonGiftCard" });
                paymentMethod.AddRange(new List<PaymentMethodModel>
                {
                    new PaymentMethodModel { Id = 1002, Name = resourceList["BankTransfer"] , CountryId = countryId, TypeId = "10022" },
                    new PaymentMethodModel { Id = 1016, Name =  resourceList["AmazonGiftCard"] , CountryId = countryId, TypeId = "1016" }
                });
            }
            return new GenericResponse<List<PaymentMethodDto>>
            {
                Status = StaticValues.Success,
                Data = _mapper.Map<List<PaymentMethodDto>>(paymentMethod)
            };
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<GenericResponse<List<WalletDto>>> GetWallets()
    {
        try
        {
            var walletToGet = await _paymentSettingRepository.GetWallets(IsEnglish);
            if (walletToGet == null)
            {
                return new GenericResponse<List<WalletDto>>
                {
                    Data = new List<WalletDto>(),
                    Status = StaticValues.Error,

                };
            }
            return new GenericResponse<List<WalletDto>>
            {
                Status = StaticValues.Success,
                Data = _mapper.Map<List<WalletDto>>(walletToGet)
            };

        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<CashoutCountriesDto>> GetCashoutCountries()
    {
        CashoutCountriesDto CashoutCountriesData = new CashoutCountriesDto();
        try
        {
            var countriesToGet = await _paymentSettingRepository.GetCountries();
            if (countriesToGet?.Count > 0)
            {
                int UserCashoutCountry = await _userRepository.GetUserPaymentCountry(UserId ?? 0);
                if (UserCashoutCountry > 0)
                {
                    CashoutCountriesData.SelectedCountryId = UserCashoutCountry;
                    CashoutCountriesData.Countries = _mapper.Map<List<CountryDto>>(countriesToGet);
                }
                else
                {
                    CashoutCountriesData.SelectedCountryId = 0;
                    List<int> MainCountriesIds = new List<int>() { 67, 195, 2, 125 };

                    var CountriesList = _mapper.Map<List<CountryDto>>(countriesToGet);
                    var MainCountriesList = CountriesList.Where(c => MainCountriesIds.Contains(c.Id)).ToList();

                    CountriesList.RemoveAll(x => MainCountriesIds.Contains(x.Id));
                    MainCountriesList.AddRange(CountriesList);
                    CashoutCountriesData.Countries = MainCountriesList;
                }
                return new GenericResponse<CashoutCountriesDto>
                {
                    Status = StaticValues.Success,
                    Data = CashoutCountriesData
                };

            }
            return new GenericResponse<CashoutCountriesDto>
            {
                Status = StaticValues.Error,
                Data = new CashoutCountriesDto() { Countries = new List<CountryDto>(), SelectedCountryId = 0 }
            };

        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<VerificationStatusDto>> SendCashoutCode(SendCashoutCodeDto model)
    {
        try
        {
            var response = new GenericResponse<VerificationStatusDto>();
            int result = 0;

            // Get status
            var statusmodel = await _cashoutRepository.GetCashoutCodeStatus(UserId ?? 0, model.UserMobileNumber);

            if (statusmodel.IsSentBefore)
            {
                result = 2;
            }
            else
            {
                result = await _cashoutRepository.SetUserCashoutCode(model.UserMobileNumber, model.CountryCode, model.SendMethodId, UserId ?? 0);
                if (result == 1)
                {
                    statusmodel.PhoneNumber = model.UserMobileNumber;
                }
            }

            // Set notices

            statusmodel.Notice = "";
            statusmodel.NoticeWithPhone = await _resourceService.GetResourceByKey("ConfirmationCodeSentAlready2") + " " + (statusmodel.PhoneNumber ?? "");
            statusmodel.CustomerServiceNotice = await _resourceService.GetResourceByKey("ReviewWithCustomerServiceAA");
            statusmodel.ContactCustomerServiceNotice = await _resourceService.GetResourceByKey("ReviewWithCustomerServiceSS");

            var statusDto = _mapper.Map<VerificationStatusDto>(statusmodel);
            // Handle result
            switch (result)
            {
                case 0:
                    response.Data = statusDto;
                    response.Message = await _resourceService.GetResourceByKey("CheckPhoneNumberAndResend");
                    break;
                case 1:
                    statusDto.CountryCode = model.CountryCode;
                    response.Data = statusDto;
                    response.Message =  await _resourceService.GetResourceByKey("CodeSent2");
                    break;
                case 2:
                    statusDto.CountryCode = model.CountryCode;
                    response.Data = statusDto;
                    response.Message = await _resourceService.GetResourceByKey("ForgetPasswordCodeAlreadySent");
                    break;
                case -1:
                    response.Data = statusDto;
                    response.Message = model.CountryCode == 966
                        ? await _resourceService.GetResourceByKey("InvalidNumber")
                        : await _resourceService.GetResourceByKey("CheckPhoneNumberValidity");
                    break;
                case -2:
                    response.Data = statusDto;
                    response.Message = await _resourceService.GetResourceByKey("ExceedResetConfirmation");
                    break;
                default:
                    response.Data = statusDto;
                    response.Message = await _resourceService.GetResourceByKey("UnknownError");
                    break;
            }

            return response;
        }
        catch(Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<bool>> CheckUserCashoutCode(string code, int methodId)
    {
        try
        {
            var response = await _cashoutRepository.CheckUserCashoutCode(UserId ?? 0, code, methodId);
            if (response)
            {
                return new GenericResponse<bool>
                {
                    Data = true,
                    Message = await _resourceService.GetResourceByKey("MobileVerified")
                };
            }
            return new GenericResponse<bool>
            {
                Errors = [await _resourceService.GetResourceByKey("pleaseMakeCodeIsCorrect")],
                Data = false
            };
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<bool>> ValidateUserMobile(string mobileNumber, string verifiedUserNumber)
    {
        try
        {
            if (mobileNumber == verifiedUserNumber)
            {
                return new GenericResponse<bool>
                {
                    Data = true,
                };
            }
            var checkResult = await _cashoutRepository.CheckUserCashoutVerifiedNumber(mobileNumber, UserId ?? 0);
            if (checkResult)
            {
                return new GenericResponse<bool>
                {
                    Data = true,
                };
            }
            return new GenericResponse<bool>
            {
                Errors = [await _resourceService.GetResourceByKey("pleaseMakeCodeIsCorrect")],
                Data = false
            };
        }
        catch (Exception)
        {
            throw;
        }
    }
    #region Update Methods
    public async Task<bool> BankFormEGUpdate(BankFormEGDto model)
    {
        UserPaymentInfo userPaymentInfo = await _paymentSettingRepository.GetUserPaymentInfo(UserId ?? 0);
        await _waffarxService.UpdateUserCashOutCountry((int)CountryEnum.Egypt, 3, UserId ?? 0);
        if (userPaymentInfo != null)
        {
            userPaymentInfo.CountryId = (int)CountryEnum.Egypt;
            if (userPaymentInfo.BankId != model.BankId)
            {
                userPaymentInfo.BankId = model.BankId;
                var bank = await _paymentSettingRepository.GetBank(model.BankId, IsEnglish);
                userPaymentInfo.BankName = bank.BankName;
            }
            if (userPaymentInfo.BranchId != model.BranchId)
            {
                userPaymentInfo.BranchId = model.BranchId;
                var bankBranch = await _paymentSettingRepository.GetBankBranch(model.BranchId, IsEnglish);
                userPaymentInfo.BankName = bankBranch.BranchName;
            }
            userPaymentInfo.AccountNumber = model.AccountNumber;
            userPaymentInfo.SwiftCode = model.SwiftCode;
            userPaymentInfo.AccountHolderName = model.AccountHolderName;
            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.BankTransfer;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.SourceId = 2;
            userPaymentInfo.IsActive = true;
            if (!userPaymentInfo.Cdate.HasValue)
            {
                userPaymentInfo.Cdate = DateTime.Now;
            }
        }
        else
        {
            userPaymentInfo = new UserPaymentInfo();
            userPaymentInfo.CountryId = (int)CountryEnum.Egypt;
            userPaymentInfo.BankId = model.BankId;
            var bank = await _paymentSettingRepository.GetBank(model.BankId, IsEnglish);

            userPaymentInfo.BankName = bank.BankName;
            userPaymentInfo.BranchId = model.BranchId;
            var bankBranch = await _paymentSettingRepository.GetBankBranch(model.BranchId, IsEnglish);

            userPaymentInfo.BankName = bankBranch.BranchName;
            userPaymentInfo.AccountNumber = model.AccountNumber;
            userPaymentInfo.SwiftCode = model.SwiftCode;
            userPaymentInfo.AccountHolderName = model.AccountHolderName;
            userPaymentInfo.SourceId = 2;
            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.BankTransfer;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.IsActive = true;
            userPaymentInfo.Cdate = DateTime.Now;

        }
        await _userRepository.UpdateUserMailingCountry(UserId ?? 0, (int)CountryEnum.Egypt);
        await SendEventToWebEngage("EgyptBank");
        var result = await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
        return result;
    }

    // Example Fawry update method
    public async Task<bool> UpdateFawry(FawryDto model)
    {

        UserPaymentInfo userPaymentInfo = await _paymentSettingRepository.GetUserPaymentInfo(UserId ?? 0);
        if (userPaymentInfo != null)
        {
            await _userRepository.UpdateUserMailingCountry(UserId ?? 0, model.CountryID);
            await _waffarxService.UpdateUserCashOutCountry(model.CountryID, 3, UserId ?? 0);

            userPaymentInfo.UserId = UserId;
            userPaymentInfo.FawryMobile = model.FawryNumber;
            userPaymentInfo.FawryName = model.FawryName;
            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.Fawry;
            userPaymentInfo.CountryId = (int)CountryEnum.Egypt;
            if (!userPaymentInfo.Cdate.HasValue)
            {
                userPaymentInfo.Cdate = DateTime.Now;
            }
            userPaymentInfo.IsActive = true;
            userPaymentInfo.SourceId = 2;

        }
        else
        {
            userPaymentInfo = new UserPaymentInfo();
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.FawryMobile = model.FawryNumber;
            userPaymentInfo.FawryName = model.FawryName;
            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.Fawry;
            userPaymentInfo.Cdate = DateTime.Now;
            userPaymentInfo.CountryId = (int)CountryEnum.Egypt;
            userPaymentInfo.IsActive = true;
            userPaymentInfo.SourceId = 2;

        }
        await SendEventToWebEngage("Fawry");
        var result = await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
        return result;
    }

    // Example Wallet update method
    public async Task<bool> UpdateWallet(WalletDataDto model)
    {

        UserPaymentInfo userPaymentInfo = await _paymentSettingRepository.GetUserPaymentInfo(UserId ?? 0);
        await _userRepository.UpdateUserMailingCountry(UserId ?? 0, (int)CountryEnum.Egypt);
        await _waffarxService.UpdateUserCashOutCountry((int)CountryEnum.Egypt, 3, UserId ?? 0);
        if (userPaymentInfo != null)
        {
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.WalletId = model.WalletID;
            userPaymentInfo.WalletName = model.WalletName;
            userPaymentInfo.WalletNo = model.WalletNumber;
            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.EWallets;
            userPaymentInfo.CountryId = (int)CountryEnum.Egypt;
            if (!userPaymentInfo.Cdate.HasValue)
            {
                userPaymentInfo.Cdate = DateTime.Now;
            }
            userPaymentInfo.IsActive = true;
            userPaymentInfo.SourceId = 2;


        }
        else
        {
            userPaymentInfo = new UserPaymentInfo();
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.WalletId = model.WalletID;
            userPaymentInfo.WalletName = model.WalletName;
            userPaymentInfo.WalletNo = model.WalletNumber;
            userPaymentInfo.PaymentMethodId = 1011;
            userPaymentInfo.Cdate = DateTime.Now;
            userPaymentInfo.CountryId = (int)CountryEnum.Egypt;
            userPaymentInfo.IsActive = true;
            userPaymentInfo.SourceId = 2;

        }
        await SendEventToWebEngage("EWallet");
        var result = await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
        return result;

    }

    // Example Non-Egypt Bank update method
    public async Task<bool> UpdatePaymentInfo(BankFormNoneEGDto model)
    {

        UserPaymentInfo userPaymentInfo = await _paymentSettingRepository.GetUserPaymentInfo(UserId ?? 0);
        if (userPaymentInfo != null)
        {
            userPaymentInfo.CountryId = CountryId;  //BankMailingCountry;
            userPaymentInfo.BillingAddress = model.BankAddress;

            userPaymentInfo.BankName = model.BankName;
            userPaymentInfo.BankAddress = model.BankAddress;
            userPaymentInfo.BankBranchName = model.BankBranchName;
            userPaymentInfo.AccountNumber = model.AccountNo;
            userPaymentInfo.SwiftCode = model.SWIFTCode;
            userPaymentInfo.Iban = model.IBAN;
            userPaymentInfo.AccountHolderName = model.AccountHolderName;
            //userPaymentInfo.Dob = model.;
            //userPaymentInfo.PostalCode = model.PostalCode;
            //userPaymentInfo.FirstName = model.FirstName;
            //userPaymentInfo.LastName = model.LastName;
            //userPaymentInfo.MiddleName = MiddleName;
            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.BankTransfer;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.SourceId = 2;
            userPaymentInfo.IsActive = true;
            //db.Entry(userPaymentInfo).State = EntityState.Modified;
            //db.SaveChanges();
            //return userPaymentInfo.Id;
        }
        else
        {
            userPaymentInfo.CountryId = CountryId; //BankMailingCountry;
            userPaymentInfo.BillingAddress = model.BankAddress;

            userPaymentInfo.BankName = model.BankName;
            userPaymentInfo.BankBranchName = model.BankBranchName;
            userPaymentInfo.BankAddress = model.BankAddress;
            userPaymentInfo.AccountNumber = model.AccountNo;
            userPaymentInfo.SwiftCode = model.SWIFTCode;
            userPaymentInfo.Iban = model.IBAN;
            userPaymentInfo.AccountHolderName = model.AccountHolderName;

            //userPaymentInfo.DOB = DOB;
            //userPaymentInfo.FirstName = FirstName;
            //userPaymentInfo.LastName = LastName;
            //userPaymentInfo.MiddleName = MiddleName;
            //userPaymentInfo.PostalCode = PostalCode;

            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.BankTransfer;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.IsActive = true;
            userPaymentInfo.SourceId = 2;
            userPaymentInfo.Cdate = DateTime.Now;

        }
        var result = await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
        return result;

    }

    // Overloaded method for countries with specific handling
    public async Task<bool> UpdatePaymentInfo(MorocoBankDto model)
    {

        UserPaymentInfo userPaymentInfo = await _paymentSettingRepository.GetUserPaymentInfo(UserId ?? 0);
        if (userPaymentInfo != null)
        {
            userPaymentInfo.CountryId = (int)CountryEnum.Morocco;
            userPaymentInfo.BillingAddress = model.BankAddress;
            userPaymentInfo.BankName = model.BankName;
            userPaymentInfo.BankAddress = model.BankAddress;
            userPaymentInfo.BankBranchName = model.BankBranchName;
            userPaymentInfo.AccountNumber = model.AccountNo;
            userPaymentInfo.SwiftCode = model.SWIFTCode;
            userPaymentInfo.Iban = model.IBAN;

            userPaymentInfo.Dob = model.DOB.HasValue ? DateOnly.FromDateTime(model.DOB.Value) : null;
            userPaymentInfo.FirstName = model.FirstName;
            userPaymentInfo.LastName = model.LastName;
            userPaymentInfo.MiddleName = model.MiddleName;
            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.BankTransfer;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.SourceId = 2;
            userPaymentInfo.IsActive = true;
        }
        else
        {
            userPaymentInfo.CountryId = (int)CountryEnum.Morocco;
            userPaymentInfo.BillingAddress = model.BankAddress;

            userPaymentInfo.BankName = model.BankName;
            userPaymentInfo.BankBranchName = model.BankBranchName;
            userPaymentInfo.BankAddress = model.BankAddress;
            userPaymentInfo.AccountNumber = model.AccountNo;
            userPaymentInfo.SwiftCode = model.SWIFTCode;
            userPaymentInfo.Iban = model.IBAN;
            userPaymentInfo.Dob = model.DOB.HasValue ? DateOnly.FromDateTime(model.DOB.Value) : null;
            userPaymentInfo.FirstName = model.FirstName;
            userPaymentInfo.LastName = model.LastName;
            userPaymentInfo.MiddleName = model.MiddleName;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.BankTransfer;
            userPaymentInfo.IsActive = true;
            userPaymentInfo.SourceId = 2;
            userPaymentInfo.Cdate = DateTime.Now;

        }
        var result = await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
        return result;
    }

    // Example Italy Bank update method
    public async Task<bool> UpdateItalyUserPaymentInfo(ItalyBankDto model)
    {

        UserPaymentInfo userPaymentInfo = await _paymentSettingRepository.GetUserPaymentInfo(UserId ?? 0);
        // Update Payment Info
        if (userPaymentInfo != null)
        {
            userPaymentInfo.CountryId = (int)CountryEnum.Italy;
            userPaymentInfo.AccountHolderName = model.AccountHolderName;
            userPaymentInfo.BankEmail = model.BanKEmail;
            userPaymentInfo.Iban = model.IBAN;
            userPaymentInfo.BillingAddress = model.AccountHolderResidentialAddress;
            userPaymentInfo.PostalCode = model.PostCode;
            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.BankTransfer;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.IsActive = true;
            userPaymentInfo.SourceId = 2;

        }
        else
        {
            userPaymentInfo = new UserPaymentInfo();
            userPaymentInfo.CountryId = (int)CountryEnum.Italy;
            userPaymentInfo.AccountHolderName = model.AccountHolderName;
            userPaymentInfo.BankEmail = model.BanKEmail;
            userPaymentInfo.Iban = model.IBAN;
            userPaymentInfo.BillingAddress = model.AccountHolderResidentialAddress;
            userPaymentInfo.PostalCode = model.PostCode;
            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.BankTransfer;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.IsActive = true;
            userPaymentInfo.SourceId = 2;
            userPaymentInfo.Cdate = DateTime.Now;
        }
        var result = await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
        return result;


    }

    // Example KSA Bank update method (using the overloaded UpdatePaymentInfo)
    public async Task<bool> UpdatePaymentInfo(KSABankDto model)
    {

        UserPaymentInfo userPaymentInfo = await _paymentSettingRepository.GetUserPaymentInfo(UserId ?? 0);
        if (userPaymentInfo != null)
        {
            userPaymentInfo.CountryId = (int)CountryEnum.SaudiArabia;
            userPaymentInfo.BillingAddress = model.BankAddress;
            userPaymentInfo.BankName = model.BankName;
            userPaymentInfo.BankAddress = model.BankAddress;
            userPaymentInfo.BankBranchName = model.BankBranchName;
            userPaymentInfo.AccountNumber = model.AccountNo;
            userPaymentInfo.SwiftCode = model.SWIFTCode;
            userPaymentInfo.Iban = model.IBAN;
            userPaymentInfo.AccountHolderName = model.AccountHolderName;
            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.BankTransfer;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.SourceId = 2;
            userPaymentInfo.IsActive = true;

        }
        else
        {
            userPaymentInfo.CountryId = (int)CountryEnum.SaudiArabia;
            userPaymentInfo.BillingAddress = model.BankAddress;
            userPaymentInfo.BankName = model.BankName;
            userPaymentInfo.BankBranchName = model.BankBranchName;
            userPaymentInfo.BankAddress = model.BankAddress;
            userPaymentInfo.AccountNumber = model.AccountNo;
            userPaymentInfo.SwiftCode = model.SWIFTCode;
            userPaymentInfo.Iban = model.IBAN;
            userPaymentInfo.AccountHolderName = model.AccountHolderName;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.BankTransfer;
            userPaymentInfo.IsActive = true;
            userPaymentInfo.SourceId = 2;
            userPaymentInfo.Cdate = DateTime.Now;

        }
        var result = await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
        return result;
    }

    // Example Credit Card update method
    public async Task<bool> CreditCardFormEGUpdate(CreditCardFormEGDto model)
    {
        UserPaymentInfo userPaymentInfo = await _paymentSettingRepository.GetUserPaymentInfo(UserId ?? 0);
        if (userPaymentInfo != null)
        {
            userPaymentInfo.CountryId = 67;
            if (userPaymentInfo.BankId != model.BankId)
            {
                userPaymentInfo.BankId = model.BankId;
                var bank = await _paymentSettingRepository.GetBank(model.BankId, IsEnglish);
                userPaymentInfo.BankName = bank.BankName;

            }
            if (userPaymentInfo.BranchId != model.BranchId)
            {
                userPaymentInfo.BranchId = model.BranchId;
                var bankBranch = await _paymentSettingRepository.GetBankBranch(model.BranchId, IsEnglish);
                userPaymentInfo.BankName = bankBranch.BranchName;
            }
            userPaymentInfo.CreditCardNumber = model.CreditCardNo;
            userPaymentInfo.SwiftCode = model.SWIFTCode;
            userPaymentInfo.AccountHolderName = model.AccountHolderName;
            userPaymentInfo.PaymentMethodId = 1013;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.IsActive = true;
            userPaymentInfo.SourceId = 2;
            if (!userPaymentInfo.Cdate.HasValue)
            {
                userPaymentInfo.Cdate = DateTime.Now;
            }
        }
        else
        {
            userPaymentInfo = new UserPaymentInfo();
            userPaymentInfo.CountryId = 67;
            userPaymentInfo.BankId = model.BankId;
            userPaymentInfo.BranchId = model.BranchId;
            var bank = await _paymentSettingRepository.GetBank(model.BankId, IsEnglish);
            userPaymentInfo.BankName = bank.BankName;

            var bankBranch = await _paymentSettingRepository.GetBankBranch(model.BranchId, IsEnglish);
            userPaymentInfo.BankName = bankBranch.BranchName;

            userPaymentInfo.CreditCardNumber = model.CreditCardNo;
            userPaymentInfo.SwiftCode = model.SWIFTCode;
            userPaymentInfo.AccountHolderName = model.AccountHolderName;

            userPaymentInfo.PaymentMethodId = 1013;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.IsActive = true;
            userPaymentInfo.SourceId = 2;
            userPaymentInfo.Cdate = DateTime.Now;

        }
        var result = await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
        return result;
    }

    // Example Amazon Gift Card update method
    public async Task<bool> AmazonGiftCardFormUpdate(AmazonGiftCardPaymentDto model)
    {

        //UpdateUserCashOutCountry(vm.CountryId, 3);
        UserPaymentInfo userPaymentInfo = await _paymentSettingRepository.GetUserPaymentInfo(UserId ?? 0);
        if (userPaymentInfo != null)
        {
            userPaymentInfo.CountryId = model.CountryId;
            userPaymentInfo.AgcodstoreId = model.GiftCardStoreId;
            userPaymentInfo.PaymentMethodId = (int)PaymentMethodEnum.AmazonGiftCard;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.IsActive = true;
            userPaymentInfo.SourceId = 2;
            if (!userPaymentInfo.Cdate.HasValue)
            {
                userPaymentInfo.Cdate = DateTime.Now;
            }

        }
        else
        {
            userPaymentInfo = new UserPaymentInfo();
            userPaymentInfo.CountryId = model.CountryId;
            userPaymentInfo.AgcodstoreId = model.GiftCardStoreId;
            userPaymentInfo.PaymentMethodId = 1016;
            userPaymentInfo.UserId = UserId;
            userPaymentInfo.IsActive = true;
            userPaymentInfo.SourceId = 2;
            userPaymentInfo.Cdate = DateTime.Now;

        }
        var result = await _paymentSettingRepository.AddOrUpdateUserPaymentInfo(userPaymentInfo);
        return result;
        //UpdateMailingCountry(vm.CountryId);
    }



    private async Task SendEventToWebEngage(string payment)
    {
        await _webEngageService.SendWebEngageEvent(new WE_EventModel
        {
            userId = UserId.Value.ToString(),
            eventName = "HasPayment",
            eventTime = DateTime.Now.ToString("yyyy-MM-ddTHH:mm:sszz00"),
            eventData = new WE_EventDataModel()
            {
                payment_name = payment,
                user_id = UserId.Value
            }

        });
    }



    #endregion
}
