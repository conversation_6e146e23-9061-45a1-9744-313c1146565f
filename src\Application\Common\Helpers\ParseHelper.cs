﻿using System;
using System.Globalization;
using System.Security.Cryptography;
using System.Text;
using Microsoft.Extensions.Logging;

namespace WaffarxWebAPIs.Application.Common.Helpers;
public class ParseHelper
{
    private readonly ILogger<ParseHelper> _logger;
    public ParseHelper(ILogger<ParseHelper> logger)
    {
        _logger = logger;

    }
    public static int? TryParseIntNullable(string val)
    {
        int outValue;
        return int.TryParse(val, out outValue) ? (int?)outValue : null;
    }
    public static Int64? TryParseInt64Nullable(string val)
    {
        Int64 outValue;
        return Int64.TryParse(val, out outValue) ? (Int64?)outValue : null;
    }
    public static bool? TryParseBoolNullable(string val)
    {
        bool outValue;
        return bool.TryParse(val, out outValue) ? (bool?)outValue : null;
    }
    public static double? TryParseDoubleNullable(string val)
    {
        double outValue;
        return double.TryParse(val, out outValue) ? (double?)outValue : null;
    }
    public static decimal? TryParseDecimalNullable(string val)
    {
        decimal outValue;
        return decimal.TryParse(val, out outValue) ? (decimal?)outValue : null;
    }
    public static decimal TryParseDecimalCurrencyNullable(string val, CultureInfo CI)
    {
        decimal outValue = 0;
        outValue = decimal.Parse(val, System.Globalization.NumberStyles.Currency, CI);
        return outValue;
    }
    public static DateTime? TryParseDateTimeNullable(string val)
    {
        DateTime outValue;
        return DateTime.TryParse(val, out outValue) ? (DateTime?)outValue : null;
    }
    public static string RemoveTrailingSlash(string Url)
    {
        try
        {
            if (!string.IsNullOrEmpty(Url))
            {
                string a = Url[Url.Length - 1].ToString();
                if (a == "/" || a == "\\")
                {
                    Url = Url.Substring(0, Url.Length - 1).ToLower();
                }
            }
        }
        catch (Exception)
        {
        }
        return Url.ToLower();
    }
    public static string NormalizeTitle(string title)
    {
        var tmp = "";
        try
        {
            tmp = title.Replace(" ", "-").Replace("\"", "-").Replace(".", "-").Replace("&", "-").Replace(":", "-").Replace(".", "-").Replace("\"", "-").Replace("\'", "-").Replace(";", "-").Replace("--'", "-").Replace("---'", "-").Replace("/''", "-").Replace("|", "-").Replace("!", "-").Replace("#", "-").Replace("%", "-").Replace("$", "-").Replace("*", "-").Replace("?", "-").Replace("؟", "-").Replace(">", "-").Replace("<", "-").Replace(",", "-").Replace("(", "-").Replace(")", "-").Replace("]", "-").Replace("[", "-").Replace("}", "-").Replace("{", "-").Replace("؛", "-").Replace("\\", "-").Replace("/", "-").Replace("+", "-");
            tmp = tmp.Replace("----", "-").Replace("---", "-").Replace("--", "-");

            if (tmp.StartsWith("-"))
                tmp = tmp.Remove(0, 1);

            if (tmp.EndsWith("-"))
                tmp = tmp.Remove(tmp.LastIndexOf('-'));

            string[] words = tmp.Split('-');
            if (words.Length > 10)
                tmp = string.Join("-", words, 0, 10);

        }
        catch
        {
        }

        return tmp;
    }

    public static long ConvertDateTimeToUnix(DateTime? DateValue)
    {
        long result = 0L;
        try
        {
            if (!DateValue.HasValue)
            {
                return result;
            }
            const double LongAdj = 1000.0;
            DateTime d = new DateTime(1970, 1, 1, 0, 0, 0, DateTimeKind.Local);
            result = (long)((DateValue.Value.ToUniversalTime() - d).TotalSeconds * LongAdj);
            //result = (long)(DateValue.Value.ToUniversalTime() - d).TotalSeconds;
            return result;
        }
        catch (Exception)
        {
            return result;
        }
    }
    public static string ConvertDateCultureToENUS(DateTime? Date, string Format)
    {
        var RetVal = "";
        try
        {
            if (Date != null && Date.HasValue)
            {
                System.Globalization.DateTimeFormatInfo DTFormat;
                DTFormat = new System.Globalization.CultureInfo("en-us").DateTimeFormat;
                DTFormat.Calendar = new System.Globalization.GregorianCalendar();
                RetVal = Date.Value.ToString(Format, DTFormat);
            }
        }
        catch
        {


        }
        return RetVal;
    }
    public static string GetSHA256Hash(SHA256 sha256Hash, string input)
    {
        if (sha256Hash == null)
            sha256Hash = SHA256.Create();
        // Convert the input string to a byte array and compute the hash.
        byte[] data = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(input));

        // Create a new Stringbuilder to collect the bytes
        // and create a string.
        StringBuilder sBuilder = new StringBuilder();

        // Loop through each byte of the hashed data 
        // and format each one as a hexadecimal string.
        for (int i = 0; i < data.Length; i++)
        {
            sBuilder.Append(data[i].ToString("x2"));
        }

        // Return the hexadecimal string.
        return sBuilder.ToString();
    }
    public static string RandomCashoutMobileCode(int length)
    {
        var RetVal = "";
        const string chars = "0123456789";
        Random random = new Random(); // Declare and initialize the 'random' variable
        RetVal = new string(Enumerable.Repeat(chars, length).Select(s => s[random.Next(s.Length)]).ToArray());
        return RetVal;
    }
}

