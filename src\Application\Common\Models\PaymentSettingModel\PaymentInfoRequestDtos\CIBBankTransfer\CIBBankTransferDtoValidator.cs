﻿namespace WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.CIBBankTransfer;
public class CIBBankTransferDtoValidator : AbstractValidator<CIBBankTransferDto>
{
    public CIBBankTransferDtoValidator()
    {
        RuleFor(x => x.AccountHolderName)
            .NotEmpty().WithMessage("AccountHoldeNameRequired")
            .Matches(@"^[A-Za-z ]*$").WithMessage("BankNote");

        RuleFor(x => x.CibAccountNumber)
            .Matches(@"\d{12,12}").WithMessage("CibAccountNumberError")
            .When(x => !string.IsNullOrEmpty(x.CibAccountNumber));
    }
}
