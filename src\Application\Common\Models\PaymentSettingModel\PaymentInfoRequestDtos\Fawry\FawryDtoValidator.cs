﻿namespace WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.Fawry;
public class FawryDtoValidator : AbstractValidator<FawryDto>
{
    public FawryDtoValidator()
    {
        RuleFor(x => x.FawryName)
            .NotEmpty().WithMessage("AccountHoldeNameRequired")
            .Matches("^[أ-يءA-Za-z ]*$").WithMessage("BankNote");

        RuleFor(x => x.FawryNumber)
            .NotEmpty()
            .Matches(@"^[0-9]{11,11}$").WithMessage("CheckPhoneNumberValidity");
    }
}
