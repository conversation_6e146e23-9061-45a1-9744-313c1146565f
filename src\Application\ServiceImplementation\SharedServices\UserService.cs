﻿using Microsoft.AspNetCore.Http;
using WaffarxWebAPIs.Application.Common.Helpers;
using WaffarxWebAPIs.Application.Common.Models;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.UserMainData;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.ExitClicks;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.User;
using WaffarxWebAPIs.Application.Interfaces;
using WaffarxWebAPIs.Application.Interfaces.SharedInterfaces;
using WaffarxWebAPIs.Domain.Constants;
using WaffarxWebAPIs.Domain.Enums;
using WaffarxWebAPIs.Domain.Interfaces.SharedInterfaces;
using WaffarxWebAPIs.Domain.Models.UserModel;
using WaffarxWebAPIs.Domain.RepositoryInterface.SharedInterfaces;
using WaffarxWebAPIs.Domain.Models.SharedModels;
using WaffarxWebAPIs.Domain.Models.UsersModels;
using WaffarxWebAPIs.Application.ServiceInterface.GeneralInterfaces;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Referal;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.VerificationDto;
using WaffarxWebAPIs.Domain.Entities;
using WaffarxWebAPIs.Application.Common.Models.HelpersModels;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.FAQ;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.ForgetPasswordDtos.CheckPassCode;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.ForgetPasswordDtos.ResetPasswordMobile;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.ForgetPasswordDtos.ResetPasswordEmail;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.DeleteUser;
using WaffarxWebAPIs.Application.Common.Models.WebEngageModels;
using Microsoft.Extensions.DependencyInjection;

namespace WaffarxWebAPIs.Application.Implementations.SharedServices;
public class UserService : BaseService, IUserService
{
    private readonly IUserRepository _userRepository;
    private readonly ICashBackRepository _cashBackRepository;
    private readonly ISharedRepository _sharedRepository;
    private readonly IResourceService _resourceService;
    private readonly ICashoutRepository _cashoutRepository;
    private readonly IMapper _mapper;
    private readonly IExitClickRepository _exitClickRepository;
    private readonly IEmailService _emailService;
    private readonly IWaffarXService _waffarXService;
    private readonly IWebEngageService _webEngageService;
    private readonly Random _random = new Random();
    private readonly IServiceScopeFactory _scopeFactory;
    public UserService(IUserRepository userRepository
        , ICashBackRepository cashBackRepository, ISharedRepository sharedRepository
        , IResourceService resourceService, ICacheService cacheService, IHttpContextAccessor httpContextAccessor
        , ICashoutRepository cashoutRepository, IMapper mapper
        , IExitClickRepository exitClickRepository, IEmailService emailService
        , IWaffarXService waffarXService,
        IWebEngageService webEngageService, IServiceScopeFactory scopeFactory) : base(httpContextAccessor)
    {
        _userRepository = userRepository;
        _cashoutRepository = cashoutRepository;
        _resourceService = resourceService;
        _mapper = mapper;
        _sharedRepository = sharedRepository;
        _cashBackRepository = cashBackRepository;
        _cashoutRepository = cashoutRepository;
        _exitClickRepository = exitClickRepository;
        _emailService = emailService;
        _waffarXService = waffarXService;
        _webEngageService = webEngageService;
        _scopeFactory = scopeFactory;
    }
    public async Task<UserPaymentInfoDto> UserPaymentInfo()
    {
        try
        {
            var userPaymentInfo = await _userRepository.GetUserPaymentInfo(UserId ?? 0, IsEnglish);
            if (userPaymentInfo == null)
            {
                return new UserPaymentInfoDto();
            }
            UserPaymentInfoDto result = new UserPaymentInfoDto();
            result = _mapper.Map<UserPaymentInfoDto>(userPaymentInfo, opts => opts.Items["IncludeId"] = false);
            if (userPaymentInfo.PaymentMethodId == (int)PaymentMethodEnum.CIBBankTransfer)
            {
                var resource = await _resourceService.GetResourceByKey("YouMustAddCIBAccountNum");
                result.CibAccountMsg = new List<string>() { resource };
            }
            if (result.PaymentMethodId == (int)PaymentMethodEnum.AmazonGiftCard)
            {
                if (result.AGCODStoreId == 8894)
                {
                    result.AGCODStoreName = await _resourceService.GetResourceByKey("AmazonKSAGiftCard");
                }
                else if (result.AGCODStoreId == 8867)
                {
                    result.AGCODStoreName = await _resourceService.GetResourceByKey("AmazonUAEGiftCard");
                }
                else if (result.AGCODStoreId == 9041)
                {
                    result.AGCODStoreName = await _resourceService.GetResourceByKey("AmazonEGPGiftCard");
                }
                result.AGCODStoreNote = await _resourceService.GetResourceByKey("AGCODSettingsNote");
            }
            if (result.PaymentMethodId == (int)PaymentMethodEnum.NeqatyProgram)
            {
                result.PageTypeId = "1019";
                result.CanChangeMethod = true;
                if (await _userRepository.GetUserProgramStateByBankId(userPaymentInfo.UserId ?? 0, (int)PaymentMethodEnum.NeqatyProgram))
                {
                    result.CanChangeMethod = false;
                }
            }
            result.FailedText = "";
            if (result.IsActive == false)
            {
                result.FailedText = await _resourceService.GetResourceByKey("InactiveCashOutMethod");
            }
            return result;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<List<OnlineExitClickDto>>> OnlineExitClickStores(int pageNo = 1, int pageSize = 50)
    {

        var query = await _exitClickRepository.GetOnlineExitClickStores(UserId ?? 0, IsEnglish, pageNo, pageSize);
        if (!query.Data.Any())
        {
            return new GenericResponse<List<OnlineExitClickDto>>
            {
                Status = StaticValues.Error,
                Data = new List<OnlineExitClickDto>()
            };
        }
        return new GenericResponse<List<OnlineExitClickDto>>
        {
            Data = query.Data.Select(s => new OnlineExitClickDto
            {
                Id = s.Id,
                AdvertiserName = s.AdvertiserName,
                AdvertiserTitle = s.AdvertiserTitle,
                CreationDate = s.CreationDate.HasValue ? ParseHelper.ConvertDateTimeToUnix(s.CreationDate) : ParseHelper.ConvertDateTimeToUnix(DateTime.Now)
            }).ToList(),
            TotalCount = query.TotalRecords,
            Status = StaticValues.Success
        };
    }
    public async Task<GenericResponse<UserInfoDto>> GetUserInfo()
    {
        try
        {
            var userToGet = await _userRepository.GetUserById(UserId ?? 0);
            if (userToGet == null)
            {
                return new GenericResponse<UserInfoDto>
                {
                    Status = StaticValues.Error,
                    Data = new UserInfoDto()
                };

            }
            return new GenericResponse<UserInfoDto>
            {
                Status = StaticValues.Success,
                Data = new UserInfoDto
                {
                    Email = userToGet.Email,
                    DisplayName = userToGet.DisplayName,
                    Mobile = await _sharedRepository.GetUserVerifiedMobileNumber(UserId ?? 0),
                    BDate = ParseHelper.ConvertDateTimeToUnix(userToGet.BDate.HasValue ? userToGet.BDate.Value.ToDateTime(TimeOnly.MinValue) : (DateTime?)null),
                    Gender = userToGet.Gender,

                }
            };
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<UserInfoDto>> UpdateUserData(UserUpdateDto user)
    {
        try
        {
            var userToUpdate = _mapper.Map<UpdateUserModel>(user);
            var result = await _userRepository.UpdateUserMainInfo(UserId ?? 0, userToUpdate);
            if (result == null)
            {
                return new GenericResponse<UserInfoDto>
                {
                    Status = StaticValues.Error,
                    Data = new UserInfoDto()
                };
            }
            _webEngageService.UpdateUserMainDataWebEngage(result);
            return new GenericResponse<UserInfoDto>
            {
                Status = StaticValues.Success,
                Data = new UserInfoDto
                {
                    Email = result.Email,
                    DisplayName = result.DisplayName,
                    Mobile = await _sharedRepository.GetUserVerifiedMobileNumber(UserId ?? 0),
                    BDate = ParseHelper.ConvertDateTimeToUnix(result.BDate.HasValue ? result.BDate.Value.ToDateTime(TimeOnly.MinValue) : (DateTime?)null),
                    Gender = result.Gender,

                }
            };
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<int>> UpdateUserFavoriteList(Guid storeGuid)
    {
        try
        {
            var updateResult = await _userRepository.UpdateUserFavoriteList(UserId ?? 0, storeGuid);
            if (updateResult > 0)
            {
                return new GenericResponse<int>
                {
                    Status = StaticValues.Success,
                    Data = updateResult,
                };
            }
            else
            {
                return new GenericResponse<int>
                {
                    Status = StaticValues.Error,
                    Data = updateResult,
                };
            }

        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<UserCurrentStageDto> GetUserCurrentStage()
    {
        UserCurrentStageDto userStagesDto = new UserCurrentStageDto();
        try
        {
            return userStagesDto = new UserCurrentStageDto()
            {
                Stage = await GetCurrentStage(),
                StageVideos = await GetVidoesLinks()
            };
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<string>> SendReferralEmail(string email)
    {
        try
        {
            UserEmailModel model = await _userRepository.CheckUserEmailForReferal(UserId ?? 0, email);
            if (model.Result > 0)
            {

                int programId = await _userRepository.GetUserProgramState(UserId ?? 0);
                var cashBackSettings = await _sharedRepository.GetCurrentPaymentCycle(programId);
                var referalLogId = await _userRepository.AddReferalLogToUser(new UserRerferalLogModel
                {
                    UserId = UserId ?? 0,
                    BonusValueFromReferal = (decimal)cashBackSettings.BonusValueFromReferal,
                    BonusValueToReferal = (decimal)cashBackSettings.BonusValueToReferal,
                    Email = email,
                    ReferralCode = model.ReferralCode,
                });
                string emailBody = await _sharedRepository.GetEmailBody(2);
                emailBody = emailBody.Replace("$$InviteCode", "https://waffarx.com/en-EG/r/" + model.ReferralCode);
                emailBody = emailBody.Replace("$$Bonus", cashBackSettings.WelcomeBonus.ToString());
                emailBody = emailBody.Replace("$$Amount", cashBackSettings.MinValue.ToString());
                emailBody = emailBody.Replace("$$StartDate", cashBackSettings.FromDate.Value.ToString("dd/MM/yyyy"));
                emailBody = emailBody.Replace("$$EndDate", cashBackSettings.ToDate.Value.ToString("dd/MM/yyyy"));
                emailBody = emailBody.Replace("$$MemberName", model.FirstName + " " + model.LastName);
                emailBody = emailBody.Replace("$$Facebook", await _sharedRepository.GetSocialNetwork(1));
                emailBody = emailBody.Replace("$$Twitter", await _sharedRepository.GetSocialNetwork(2));
                emailBody = emailBody.Replace("{{refcode}}", model.ReferralCode);
                emailBody = emailBody.Replace("{{type}}", "5");
                emailBody = emailBody.Replace("{{transid}}", referalLogId.ToString());

                await _emailService.SendEmailAsync(new Common.Models.EmailModels.MailRequest
                {
                    Body = emailBody,
                    Subject = model.FirstName + " " + model.LastName + " has referred you to WaffarX",
                    ToEmail = email
                });
            }
            string msg = "";
            switch (model.Result)
            {
                case -1:
                    msg = await _resourceService.GetResourceByKey("ThisUserHasAlreadyBeenReferred");
                    break;
                case -2:
                    msg = await _resourceService.GetResourceByKey("YouCannotReferYourself");
                    break;
                case -3:

                    msg = await _resourceService.GetResourceByKey("ThisEmailIsAlreadyRegistered");
                    break;
                case 1:
                    msg = await _resourceService.GetResourceByKey("Emailsentsuccessfully");
                    break;
            }
            return new GenericResponse<string>
            {
                Status = model.Result == 1 ? StaticValues.Success : StaticValues.Error,
                Data = msg
            };
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<ReferalListDto>> GetUserReferals()
    {
        try
        {
            var referals = await _userRepository.GetUserReferalLog(UserId ?? 0);

            ReferalListDto referalListDto = new ReferalListDto();
            if (referals?.Count > 0)
            {
                referals.ForEach(a => a.CreationDate = ParseHelper.ConvertDateTimeToUnix(a.CDate) / 1000);
                referalListDto.Invited = _mapper.Map<List<ReferalLogDto>>(referals.Where(a => !a.ToUserId.HasValue || a.ToUserId == 0).ToList(), opts => opts.Items["IncludeId"] = false);
                referalListDto.Accepted = _mapper.Map<List<ReferalLogDto>>(referals.Where(a => a.ToUserId.HasValue && a.ToUserId > 0).ToList(), opt => opt.Items["IncludeId"] = false);
                referalListDto.Qualified = _mapper.Map<List<ReferalLogDto>>(referals.Where(a =>
                    a.IsUserFromCompleted.GetValueOrDefault() == 1 &&
                    a.IsUserFromCompleted.HasValue &&
                    a.IsUserToCompleted.GetValueOrDefault() == 1 &&
                    a.IsUserToCompleted.HasValue
                ).ToList(), opts => opts.Items["IncludeId"] = false);
            }

            int programId = await _userRepository.GetUserProgramState(UserId ?? 0);
            var referSetting = await _sharedRepository.GetCurrentCycleForReferal(programId);

            referalListDto.ReferalBonus = referSetting.BonusValueFromReferal ?? 2.5m;
            referalListDto.Currency = "$";
            string amount = referalListDto.ReferalBonus.ToString("N2") + " " + referalListDto.Currency;
            referalListDto.ReferalText = string.Format(await _resourceService.GetResourceByKey("MobileReferText"), amount);
            referalListDto.ReferalCode = await _userRepository.GetUserReferalCode(UserId ?? 0);
            referalListDto.ReferalStart = ParseHelper.ConvertDateTimeToUnix(referSetting.FromDate.Value.ToDateTime(new TimeOnly(0, 0)));
            referalListDto.ReferalEnd = ParseHelper.ConvertDateTimeToUnix(referSetting.ToDate.Value.ToDateTime(new TimeOnly(0, 0)));
            referalListDto.HowReferalWork = new StaticPageDto();
            var howReferalPage = await _sharedRepository.StaticPageById(12, IsEnglish);
            if (howReferalPage != null)
                referalListDto.HowReferalWork = _mapper.Map<StaticPageDto>(howReferalPage);

            return new GenericResponse<ReferalListDto>
            {
                Status = StaticValues.Success,
                Data = referalListDto
            };

        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<List<StageVideos>> GetVidoesLinks()
    {
        List<StageVideos> stages = new List<StageVideos>();
        List<string> videos = new List<string>();
        try
        {
            if (CountryId == 67)
            {
                var userProgram = await _userRepository.GetUserProgramState(UserId ?? 0);
                if (userProgram == (int)BankEnum.CIB)
                {
                    videos = new List<string>()
                    {
                        "_pMI_SV-4UM",
                        "0mdJtJxNg2k",
                        "bmtV6-D2eO4"
                    };
                }
                else
                {
                    if (!IsEnglish)
                    {

                        videos = new List<string>()
                        {   "YFVD_b4myE8",
                            "mLtgoTR5d0g",
                            "9sac0bsmNvQ"
                        };
                    }
                    else
                    {
                        videos = new List<string>()
                        {    "tmgHlGEbZlY",
                             "e1x0dOJnPe0",
                             "SlNq_lxgfIo"
                        };
                    }
                }
            }
            else if (CountryId == (int)CountryEnum.SaudiArabia || CountryId == (int)CountryEnum.kuwait)
            {
                var userProgram = await _userRepository.GetUserProgramState(UserId ?? 0);
                if (userProgram == (int)BankEnum.Mobily)
                {
                    videos = new List<string>()
                    {
                        "yDCOro-tmbY",
                        "YtlgViC_zcY",
                        "wp8ZJQw1s5I"
                    };
                }
                else
                {
                    if (!IsEnglish)
                    {

                        videos = new List<string>()
                        {
                            "5H-XKGhJd5M",
                            "dQ8G27GCy60",
                            "QpdopppEN5Q"
                        };
                    }
                    else
                    {
                        videos = new List<string>()
                        {     "So3cmB_2CC0",
                              "DsYy2d2mas0",
                              "fXIYwr7nJi4"
                        };
                    }
                }

            }
            else if (CountryId == (int)CountryEnum.UAE)
            {
                if (!IsEnglish)
                {
                    videos = new List<string>()
                    {
                        "HRA8rLYMilY",
                        "k2ZFZ-OBvuY",
                        "D-opGe1jH1U"
                    };
                }
                else
                {
                    videos = new List<string>()
                    {       "qdiFLwqYTR8",
                            "AzFz-ZOxwIo",
                            "Vp8kIIpNmlE"
                    };
                }
            }
            else
            {
                if (!IsEnglish)
                {

                    videos = new List<string>()
                    {   "YFVD_b4myE8",
                            "mLtgoTR5d0g",
                            "9sac0bsmNvQ"
                    };
                }
                else
                {
                    videos = new List<string>()
                    {    "tmgHlGEbZlY",
                            "e1x0dOJnPe0",
                            "SlNq_lxgfIo"
                    };
                }
            }
            int Index = 0;
            List<string> resourcesList = new List<string>() { "shop", "Cashback", "cashOut" };
            List<string> images = new List<string>() { "StepsVideoImg01.png", "StepsVideoImg02.png", "StepsVideoImg03.png" };
            foreach (var item in videos)
            {
                StageVideos stage = new StageVideos()
                {
                    Name = await _resourceService.GetResourceByKey(resourcesList[Index]),
                    VideoLink = item,
                    Image = (AppSettings.WaffarXSettings.BaseImgUrl + "img/" + images[Index]).ToLower()
                };
                stages.Add(stage);
                Index++;
            }
            return stages;
        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<int> GetCurrentStage()
    {
        try
        {
            var Cashback = await _cashBackRepository.UserAllCashbacksByCurrency(UserId ?? 0);
            UserPaymentInfoModel userPaymentInfo = await _userRepository.GetUserPaymentInfo(UserId ?? 0, IsEnglish);
            ReferalSettingModel currentPaymentCycle = await _sharedRepository.GetCurrentPaymentCycle(0);
            decimal MinValueToCashOut = 0m;
            if (userPaymentInfo != null && userPaymentInfo.PaymentMethodId > 0)
            {
                MinValueToCashOut = (userPaymentInfo.CountryID == (int)CountryEnum.Egypt ? currentPaymentCycle.MinCashbackValue.Value : currentPaymentCycle.MinBankTransferNonEG.Value);
            }
            else
            {
                MinValueToCashOut = (CountryId == (int)CountryEnum.Egypt ? currentPaymentCycle.MinCashbackValue.Value : currentPaymentCycle.MinBankTransferNonEG.Value);
            }

            if (Cashback != null)
            {
                if (Cashback.PendingNotResolvedOnPurchase <= 0 && Cashback.ConfirmedNotResolvedOnPurchase <= 0 && Cashback.CashedOut <= 0 && Cashback.ApprovedBonus == 2.5m)
                {
                    return 1;
                }
                else if (Cashback.ConfirmedNotResolvedOnPurchase < MinValueToCashOut && Cashback.CashedOut <= 0)
                {
                    return 2;
                }
                else if (Cashback.ConfirmedNotResolvedOnPurchase >= MinValueToCashOut &&
                (userPaymentInfo == null || userPaymentInfo.Id <= 0))
                {
                    return 3;
                }
                else if (Cashback.ConfirmedNotResolvedOnPurchase >= MinValueToCashOut &&
                userPaymentInfo != null && userPaymentInfo.Id > 0)
                {
                    return 4;
                }
                else if (Cashback.CashedOut > 0)
                {
                    return 4;
                }

            }
            return 0;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<UserDto>> VerifyUserMobile(VerifyMobileNumberDto model)
    {
        var response = new GenericResponse<UserDto>();
        try
        {
            int userId = await _userRepository.GetUserByGuid(model.userId);
            bool IsVerifiedUser = await _sharedRepository.IsVerifiedUser(userId);
            if (!IsVerifiedUser)
            {
                AppUsersVerification appUsersVerification = await _sharedRepository.GetLastVerificationByUserId(userId);
                if (appUsersVerification != null)
                {
                    var IsVerifiedBefore = await _sharedRepository.IsVerifiedBefore(appUsersVerification.PhoneNumber);
                    if (IsVerifiedBefore)
                    {
                        response.Status = StaticValues.Error;
                        response.Errors = new List<string>() { await _resourceService.GetResourceByKey("MobileNumberVerifiedBefore") };
                        return response;
                    }
                    if (appUsersVerification.Code.Trim() == model.verificationCode.Trim())
                    {
                        await _waffarXService.UpdateUserCashOutCountry(_waffarXService.GetCountryIdByCountryCode(appUsersVerification.CountryCode.Value), (int)CashoutCountryEnum.Verification, userId);

                        appUsersVerification.VerificationStatus = true;
                        await _sharedRepository.UpdateAppUsersVerification(appUsersVerification);
                        await _userRepository.UpdateUserVerification(userId);

                        _webEngageService.UpdateUserMobileAndSubscribtionWebEngage(userId, 1, appUsersVerification.PhoneNumberHashed, false);
                        response.Status = StaticValues.Success;
                        response.Data = await GetUser(userId);
                    }
                    else
                    {
                        response.Status = StaticValues.Error;
                        response.Errors = new List<string>() { await _resourceService.GetResourceByKey("verificationcodecorrect") };
                    }
                }
            }
            else
            {
                response.Status = StaticValues.Error;
                response.Errors = new List<string>() { await _resourceService.GetResourceByKey("verificationcodecorrect") };
            }
            return response;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<UserDto> GetUser(int userId)
    {
        try
        {
            var userToGet = await _userRepository.GetUserById(UserId ?? 0);
            if (userToGet == null)
            {
                return null;

            }
            var UserCashBacks = await _cashBackRepository.UserAllCashbacksByCurrency(userId);
            bool isVerified = await _sharedRepository.IsVerifiedUser(userId);
            return new UserDto
            {
                Guid = userToGet.Guid,
                FirstName = userToGet.FirstName,
                LastName = userToGet.LastName,
                LifeTimeCashBack = UserCashBacks?.LifeTimeCashBsck ?? 0m,
                IsVerified = isVerified,
                UserProgress = CalculateProgress(userToGet, isVerified),
                CardsCount = await _userRepository.GetUserCardsCount(userId),
                ProgramId = await _userRepository.GetUserProgramState(userId),

            };
        }
        catch (Exception)
        {
            throw;
        }
    }
    private int CalculateProgress(AppUserModel user, bool isVerified)
    {
        int userProgress = 40;
        if (isVerified == true)
        {
            userProgress += 20;
        }
        if (user.Gender != null)
        {
            userProgress += 20;
        }
        if (user.BDate != null)
        {
            userProgress += 20;
        }
        return userProgress;
    }
    public async Task<GenericResponse<bool>> UpdateEmailSubscribe()
    {
        try
        {
            var ResponseModel = await _userRepository.UpdateEmailSubscribe(UserId ?? 0, CountryId);
            _webEngageService.UpdateUserMobileAndSubscribtionWebEngage(UserId ?? 0, 2, "", ResponseModel.Subscription);
            return new GenericResponse<bool>
            {
                Data = ResponseModel.Status,
                Status = StaticValues.Success,
            };
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<UserEmailSubscribeDto>> GetUserEmailSubscribe()
    {
        try
        {
            var resourceList = await _resourceService.GetResourcesList(new List<string>() { "EmailSubscription", "Getthelatestcashbackalerts", "StayuptodateonthelatestmoneysavingcouponsandhotdealsfromthestoresyouloveMakesuretoaddyourFavoritesStorestoyourDashboardtogetthemostrelevantdeals" });
            return new GenericResponse<UserEmailSubscribeDto>
            {
                Status = StaticValues.Success,
                Data = new UserEmailSubscribeDto
                {
                    StatusId = await _userRepository.GetUserEmailSubscribe(UserId ?? 0),
                    EmailSubscriptionContnet = resourceList["EmailSubscription"],
                    LatestcashbackalertsContent = resourceList["Getthelatestcashbackalerts"],
                    HeadingContent = resourceList["StayuptodateonthelatestmoneysavingcouponsandhotdealsfromthestoresyouloveMakesuretoaddyourFavoritesStorestoyourDashboardtogetthemostrelevantdeals"]
                },
            };

        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<long>> ForgetPasswordByEmail(string email)
    {
        GenericResponse<long> response = new GenericResponse<long>();
        try
        {
            int userId = await _userRepository.GetUserIdByEmail(email);
            if (userId <= 0)
            {
                response.Data = 0;
                response.Status = StaticValues.Error;
                response.Errors = new List<string>() { await _resourceService.GetResourceByKey("InvalidEmail") };
                return response;
            }
            string text = await GeneratePassCode(50);
            long PassId = await _userRepository.AddUserForgetPassword(new AppUserForgetPassword
            {
                UserId = userId,
                PassCode = text,
                Cdate = DateTime.Now,
                IsUsed = false,
            });
            string EmailTemplate;
            int programId = await _userRepository.GetUserProgramState(UserId ?? 0);
            if (programId == (int)BankEnum.CIB)
            {
                EmailTemplate = await _sharedRepository.GetEmailBody(32);
            }
            else
            {
                EmailTemplate = await _sharedRepository.GetEmailBody(27);
            }

            string str = "https://waffarx.com/en-us/resetpassword?passcode=" + text + "&pid=" + PassId;

            string EmailBody = EmailTemplate.Replace("$$ResetPasswordLink$$", str);

            await _emailService.SendEmailAsync(new Common.Models.EmailModels.MailRequest
            {
                Body = EmailBody,
                Subject = "Set Your New WaffarX Password",
                ToEmail = email
            });
            response.Data = 1;
            response.Status = StaticValues.Success;
            response.Message = await _resourceService.GetResourceByKey("ForgetPasswordEmailSent");
            return response;
        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<string> GeneratePassCode(int length)
    {
        var RetVal = "";
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789";
        RetVal = new string(Enumerable.Repeat(chars, length)
          .Select(s => s[_random.Next(s.Length)]).ToArray());
        var Existed = await _sharedRepository.IsExitedForgetPasswordCode(RetVal);
        if (Existed)
        {
            RetVal = await GeneratePassCode(length);
        }
        return RetVal;
    }
    public async Task<GenericResponse<int>> IsUsedPassCode(CheckPassCodeDto passCodeDto)
    {
        bool result = true;
        GenericResponse<int> response = new GenericResponse<int>();
        try
        {
            AppUserForgetPassword appUserForgetPassword = await _userRepository.GetUserForgetPassword(passCodeDto.PId, passCodeDto.PassCode);
            if (appUserForgetPassword == null)
            {
                result = false;
            }
            else
            {
                bool IsExpired = (((DateTime.Now - appUserForgetPassword.Cdate).TotalDays >= 7.0) ? true : false);
                if (appUserForgetPassword.IsUsed || IsExpired)
                {
                    result = false;
                }
            }
            response.Status = result ? StaticValues.Success : StaticValues.Error;
            response.Data = result ? 1 : 0;
            return response;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<int>> ResetPasswordByMobile(ResetPasswordMobileDto resetPasswordDto)
    {
        var response = new GenericResponse<int>();
        try
        {
            MobileUsersForgetPassword mobileUsersForgetPassword = await _userRepository.GetMobileForgetPasswordByCode(resetPasswordDto.Code);
            if (mobileUsersForgetPassword != null)
            {
                PasswordModel hashedPassword = PasswordHelper.CreateNewPasswordHashed(resetPasswordDto.Password);

                int Id = await _userRepository.UpdateUserPassword(mobileUsersForgetPassword.UserId ?? 0, _mapper.Map<PasswordUpdateModel>(hashedPassword));
                if (Id > 0)
                {
                    response.Status = StaticValues.Success;
                    response.Data = 1;
                    response.Message = await _resourceService.GetResourceByKey("UpdatedSuccessfully");
                    return response;
                }
            }
            response.Status = StaticValues.Error;
            response.Data = 0;
            response.Message = await _resourceService.GetResourceByKey("ErrorHappend");
            return response;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<int>> ResetPasswordByEmail(ResetPasswordEmailDto resetPasswordDto)
    {
        var response = new GenericResponse<int>();
        try
        {
            AppUserForgetPassword appUserForgetPassword = await _userRepository.GetUserForgetPassword(resetPasswordDto.PId, resetPasswordDto.PassCode);
            if (appUserForgetPassword != null && appUserForgetPassword.IsUsed == false)
            {
                PasswordModel hashedPassword = PasswordHelper.CreateNewPasswordHashed(resetPasswordDto.Password);

                int Id = await _userRepository.UpdateUserPassword(appUserForgetPassword.UserId, _mapper.Map<PasswordUpdateModel>(hashedPassword));
                if (Id > 0)
                {
                    appUserForgetPassword.IsUsed = true;
                    await _userRepository.UpdateUserForgetPassword(appUserForgetPassword);

                    response.Status = StaticValues.Success;
                    response.Data = 1;
                    response.Message = await _resourceService.GetResourceByKey("UpdatedSuccessfully");
                    return response;
                }
            }
            response.Status = StaticValues.Error;
            response.Data = 0;
            response.Message = await _resourceService.GetResourceByKey("ErrorHappend");
            return response;
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<GenericResponse<List<AppUserDeleteReasonDto>>> GetAccountDeletionReasonsList()
    {
        try
        {
            var res = await _userRepository.DeletionReasonsList(IsEnglish);
            if (res == null)
            {
                return new GenericResponse<List<AppUserDeleteReasonDto>>
                {
                    Status = StaticValues.Error,
                    Data = new List<AppUserDeleteReasonDto>()
                };

            }
            return new GenericResponse<List<AppUserDeleteReasonDto>>
            {
                Status = StaticValues.Success,
                Data = _mapper.Map<List<AppUserDeleteReasonDto>>(res)
            };

        }
        catch (Exception)
        {
            throw;
        }
    }

    public  async Task<GenericResponse<bool>> DeleteUserAccount(DeleteAccountDto deleteUserDto)
    {
        try
        {
            return await DeleteUserAccountv2(deleteUserDto);

            //_ = DeleteAccountProcess(deleteUserDto);

            //var resourceList = await _resourceService.GetResourcesList(["RequestReceived", "DeleteAcountInProgress"]);
            //return new GenericResponse<bool>
            //{
            //    Status = StaticValues.Success,
            //    Data = true,
            //    Message = $"{resourceList["RequestReceived"]} \n {resourceList["DeleteAcountInProgress"]}"
            //};

        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<GenericResponse<bool>> DeleteAccountProcess(DeleteAccountDto deleteAccountDto)
    {
        DeleteAccountModel deleteAccountModel = new DeleteAccountModel
        {
            ReasonId = deleteAccountDto.ReasonId,
            ReasonText = deleteAccountDto.ReasonText,
            UserId = UserId ?? 0,
        };
        var requestId = await _userRepository.InsertDeletedUserRequest(deleteAccountModel);
        if (requestId > 0)
        {
            await _webEngageService.UpdateUserState(UserId ?? 0);
            var deleteResult = await _userRepository.DeletUserAccountData(UserId ?? 0);
            if (deleteResult > 0)
            {
                await _userRepository.UpdateDeleteUserRequest(UserId ?? 0);

                WEDeleteResponse ErasureResult = await _webEngageService.ErasureUserData(UserId ?? 0);
                if (ErasureResult != null && ErasureResult.status.ToLower() == "success" && !string.IsNullOrEmpty(ErasureResult.data.message))
                {
                    await _userRepository.UpdateDeleteUserRequest(UserId ?? 0, ErasureResult.data.subject_request_id, ErasureResult.data.message);
                }
            }
        }
        return new GenericResponse<bool>
        {
            Status = StaticValues.Success,
            Data = true
        };
    }
    public async Task<GenericResponse<bool>> DeleteUserAccountv2(DeleteAccountDto deleteUserDto)
    {
        try
        {
            // Store the current userId as it won't be available in the background task
            var userId = UserId ?? 0;

            // Start background task for deletion process
            _ = Task.Run(async () =>
            {
                // Create a new scope for the background operation
                using var scope = _scopeFactory.CreateScope();

                // Resolve required services within the new scope
                var userRepo = scope.ServiceProvider.GetRequiredService<IUserRepository>();
                var webEngageService = scope.ServiceProvider.GetRequiredService<IWebEngageService>();

                try
                {
                    // Create delete request model
                    DeleteAccountModel deleteAccountModel = new DeleteAccountModel
                    {
                        ReasonId = deleteUserDto.ReasonId,
                        ReasonText = deleteUserDto.ReasonText,
                        UserId = userId,
                    };

                    // Insert delete request
                    var requestId = await userRepo.InsertDeletedUserRequest(deleteAccountModel);
                    if (requestId <= 0)
                    {
                        return;
                    }

                    // Update WebEngage state
                    await webEngageService.UpdateUserState(userId);

                    // Delete user data
                    var deleteResult = await userRepo.DeletUserAccountData(userId);
                    if (deleteResult > 0)
                    {
                        // Update delete request status
                        await userRepo.UpdateDeleteUserRequest(userId);

                        // Call WebEngage erasure
                        WEDeleteResponse erasureResult = await webEngageService.ErasureUserData(userId);
                        if (erasureResult?.status?.ToLower() == "success" &&
                            !string.IsNullOrEmpty(erasureResult.data.message))
                        {
                            await userRepo.UpdateDeleteUserRequest(
                                userId,
                                erasureResult.data.subject_request_id,
                                erasureResult.data.message
                            );
                        }
                    }
                }
                catch (Exception)
                {

   
                }
            });

            var resourceList = await _resourceService.GetResourcesList(["RequestReceived", "DeleteAcountInProgress"]);

            return new GenericResponse<bool>
            {
                Status = StaticValues.Success,
                Data = true,
                Message = $"{resourceList["RequestReceived"]} \n {resourceList["DeleteAcountInProgress"]}"
            };
        }
        catch (Exception )
        {
            throw;
        }
    }
}
