﻿using WaffarxWebAPIs.Domain.Models.CashBackModels;
using WaffarxWebAPIs.Domain.Models.CashoutModels;
namespace WaffarxWebAPIs.Domain.RepositoryInterface.SharedInterfaces;
public interface ICashoutRepository
{
    public Task<int> GetCashoutCountryById(int cashoutId, int userId);
    public Task<AgcodCashOutDetailsModel> GetAmazonGiftCardData(int cashoutId, int paymentCycleId, string paymentMethodName, bool isEnglish);
    Task<List<CashBackModel>> GetCashBackByCashoutId(CashbackInputsModel inputsModel);
    Task<CashOutModel> UserCashOutPendingOrRevised(int userId, int paymentCycleId, int cashoutCountryId, bool isEnglish);
    Task<VerificationStatusModel> GetCashoutCodeStatus(int userId, string mobileNumber);
    Task<int> SetUserCashoutCode(string mobileNumber, int countryCode, int methodId, int userId);
    Task<bool> CheckUserCashoutCode(int userId, string code, int methodId);
    Task<bool> CheckUserCashoutVerifiedNumber(string mobileNumber, int userId);
    Task UpdateLastPendingCashoutPaymentMethod(int userId, int paymentMethodId);

}
