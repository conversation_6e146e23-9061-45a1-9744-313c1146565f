﻿namespace WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormEG;
public class BankFormEGDtoValidator : AbstractValidator<BankFormEGDto>
{
    public BankFormEGDtoValidator()
    {
        RuleFor(x => x.AccountHolderName)
            .NotEmpty().WithMessage("AccountHoldeNameRequired")
            .Matches(@"^[A-Za-z ]*$").WithMessage("BankNote");

        RuleFor(x => x.BankId)
            .NotEmpty().WithMessage("BankRequired")
            .Must(bankId => bankId.ToString().All(char.IsDigit)).WithMessage("BankNote")
            .When(x => x.BankId != 0);

        RuleFor(x => x.BranchId)
            .NotEmpty().WithMessage("BranchRequired")
            .Must(branchId => branchId.ToString().All(char.IsDigit)).WithMessage("BankNote")
            .When(x => x.BranchId != 0);

        RuleFor(x => x.AccountNumber)
            .NotEmpty().WithMessage("AccountNumberRequired")
            .Matches("^[A-Za-z0-9]*$").WithMessage("BankNote")
            .MaximumLength(30).WithMessage("MaxAccountNumber");

        RuleFor(x => x.SwiftCode)
            .NotEmpty().WithMessage("SWIFTCodeRequired")
            .MaximumLength(15).WithMessage("SwiftCodeMaxlength")
            .Matches("^[A-Za-z0-9]*$").WithMessage("BankNote");
    }
}
