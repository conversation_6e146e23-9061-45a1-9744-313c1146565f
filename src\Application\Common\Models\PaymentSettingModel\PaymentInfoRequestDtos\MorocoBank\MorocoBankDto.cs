﻿namespace WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.MorocoBank;
public class MorocoBankDto : BasePaymentMethodDto, IBankTransferDto, IMoroccoSpecificDto
{
    public string FirstName { get; set; }
    public string MiddleName { get; set; }
    public string LastName { get; set; }
    public DateTime? DOB { get; set; }
    public string BankName { get; set; }
    public string AccountHolderResidentialAddress { get; set; }
    public string BankBranchName { get; set; }
    public string BankAddress { get; set; }
    public string AccountNo { get; set; }
    public string SWIFTCode { get; set; }
    public string PostCode { get; set; }
    public string IBAN { get; set; }
    public int CountryID { get; set; }

    public string AccountHolderName { get; set; }
}
