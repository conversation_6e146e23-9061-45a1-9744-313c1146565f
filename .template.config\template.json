{"$schema": "http://json.schemastore.org/template", "author": "<PERSON><PERSON><PERSON><PERSON>", "classifications": ["Web", "SPA", "ASP.NET", "Clean Architecture"], "name": "Clean Architecture Solution", "description": "A Clean Architecture Solution Template for creating a Single-Page Application (SPA) with ASP.NET Core.", "identity": "Clean.Architecture.Solution.CSharp", "groupIdentity": "Clean.Architecture.Solution", "shortName": "ca-sln", "tags": {"language": "C#", "type": "project"}, "sourceName": "CleanArchitecture", "preferNameDirectory": true, "symbols": {"ClientFramework": {"type": "parameter", "datatype": "choice", "choices": [{"choice": "Angular", "description": "Use Angular"}, {"choice": "React", "description": "Use React"}, {"choice": "None", "description": "Web API only"}], "defaultValue": "Angular", "description": "The type of client framework to use"}, "UseAngular": {"type": "computed", "value": "(ClientFramework == \"Angular\")"}, "UseReact": {"type": "computed", "value": "(ClientFramework == \"React\")"}, "UseApiOnly": {"type": "computed", "value": "(ClientFramework == \"None\")"}, "UseSQLite": {"type": "parameter", "datatype": "bool", "defaultValue": "false", "description": "Whether to use SQLite instead of LocalDB."}, "PipelineProvider": {"type": "parameter", "datatype": "choice", "choices": [{"choice": "a<PERSON>do", "description": "Azure Pipelines"}, {"choice": "github", "description": "GitHub Actions"}], "defaultValue": "Azure", "description": "The pipeline provider to use (github for Github Actions and azdo for Azure Pipelines)."}, "UseAzurePipelines": {"type": "computed", "value": "(PipelineProvider == \"azdo\")"}, "UseGithubActions": {"type": "computed", "value": "(PipelineProvider == \"github\")"}, "UseLocalDB": {"type": "computed", "value": "(!UseSQLite)"}, "caRepositoryUrl": {"type": "generated", "generator": "constant", "replaces": "caRepositoryUrl", "parameters": {"value": "https://github.com/jasontaylordev/CleanArchitecture"}}, "caPackageVersion": {"type": "generated", "generator": "constant", "replaces": "caPackageVersion", "parameters": {"value": "8.0.6"}}}, "sources": [{"source": "./", "target": "./", "exclude": [".azure/**/*", ".template.config/**/*", "templates/**/*", "**/*.filelist", "**/*.user", "**/*.lock.json", "*.nuspec"], "rename": {"README-template.md": "README.md"}, "modifiers": [{"condition": "(UseAngular)", "exclude": ["src/Web/ClientApp-React/**", "src/Web/Endpoints/Users.cs", "src/Web/Templates/**", "src/Web/config-react.nswag", "src/Web/config-webapi.nswag", "src/Web/Web-webapi.http"]}, {"condition": "(UseReact)", "exclude": ["src/Web/ClientApp/**", "src/Web/Endpoints/Users.cs", "src/Web/config.nswag", "src/Web/config-webapi.nswag", "src/Web/Web-webapi.http"], "rename": {"config-react.nswag": "config.nswag", "ClientApp-React": "ClientApp"}}, {"condition": "(UseApiOnly)", "exclude": ["src/Web/ClientApp/**", "src/Web/ClientApp-React/**", "src/Web/Templates/**", "src/Web/config.nswag", "src/Web/config-react.nswag", "src/Web/Web.http", "tests/Web.AcceptanceTests/**"], "rename": {"config-webapi.nswag": "config.nswag", "Web-webapi.http": "Web.http"}}, {"condition": "(UseAzurePipelines)", "exclude": [".github/**/*"]}, {"condition": "(UseGithubActions)", "exclude": [".azdo/**/*"]}, {"condition": "(UseLocalDB)", "exclude": ["src/Infrastructure/Data/SQLite/**", "src/Web/appsettings.SQLite.json", "src/Web/app.db", "tests/Application.FunctionalTests/SqliteTestDatabase.cs"]}, {"condition": "(UseSQLite)", "exclude": ["src/Infrastructure/Data/Migrations/**", "src/Web/appsettings.json", "tests/Application.FunctionalTests/SqlServerTestDatabase.cs", "tests/Application.FunctionalTests/TestcontainersTestDatabase.cs"], "rename": {"src/Infrastructure/Data/SQLite/": "src/Infrastructure/Data/Migrations/", "appsettings.SQLite.json": "appsettings.json", "TestDatabase.Sqlite.cs": "TestDatabase.cs"}}]}]}