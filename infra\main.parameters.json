{"$schema": "https://schema.management.azure.com/schemas/2019-04-01/deploymentParameters.json#", "contentVersion": "*******", "parameters": {"environmentName": {"value": "${AZURE_ENV_NAME}"}, "location": {"value": "${AZURE_LOCATION}"}, "principalId": {"value": "${AZURE_PRINCIPAL_ID}"}, "sqlAdminPassword": {"value": "$(secretOrRandomPassword ${AZURE_KEY_VAULT_NAME} sqlAdminPassword)"}, "appUserPassword": {"value": "$(secretOrRandomPassword ${AZURE_KEY_VAULT_NAME} appUserPassword)"}}}