﻿using WaffarxWebAPIs.Domain.Models.UserModel;
using WaffarxWebAPIs.Domain.Models.CashoutModels;
using WaffarxWebAPIs.Domain.Models.UsersModels;
using WaffarxWebAPIs.Domain.Models.ReferalModels;

namespace WaffarxWebAPIs.Domain.Interfaces.SharedInterfaces;
public interface IUserRepository
{
    Task<int> GetUserByGuid(string Guid);
    Task<AppUsersVerification> GetVerificationByUserId(int userId);
    Task<int> GetUserProgramState(int userId);
    Task<bool> GetUserProgramStateByBankId(int userId,int bankId);
    Task<List<CashOutModel>> GetUserCashouts(int userId, bool isEnglish);
    Task<AppUserModel> GetUserById(int userId);
    Task<AppUserModel> UpdateUserMainInfo(int userId, UpdateUserModel User);
    Task<int> GetUserCardsCount(int userId);
    Task<UserPaymentInfoModel> GetUserPaymentInfo(int userid, bool isEnglish);
    Task<int> UpdateUserFavoriteList(int userId, Guid storeGuid);
    Task<UserEmailModel> CheckUserEmailForReferal(int userId, string email);
    Task<long> AddReferalLogToUser(UserRerferalLogModel model);
    Task<List<ReferalLogModel>> GetUserReferalLog(int userId);
    Task<int> UpdateUserVerification(int userId);
    Task<UpdateEmailSubscribeResponseModel> UpdateEmailSubscribe(int userId, int? countryId = (int)CountryEnum.Global); 
    Task<bool> GetUserEmailSubscribe(int userId);
    Task<string> GetUserReferalCode(int userId);
    Task<UserPaymentInfoModel> GetUserPaymentInfoByUserId(int userId);
    Task<int> GetUserIdByEmail(string email);
    Task<long> AddUserForgetPassword(AppUserForgetPassword entity);
    Task<AppUserForgetPassword> GetUserForgetPassword(long id, string code);
    Task<MobileUsersForgetPassword> GetMobileForgetPasswordByCode(string code);
    Task<int> UpdateUserPassword(int userId, PasswordUpdateModel hashedPassword);
    Task<long> UpdateUserForgetPassword(AppUserForgetPassword entity);
    Task<List<AppUserDeleteReasonModel>> DeletionReasonsList(bool isEnglish);
    Task<int> InsertDeletedUserRequest(DeleteAccountModel model);
    Task<bool> UpdateDeleteUserRequest(int userId, string requestId = "", string message = "");
    Task<int> DeletUserAccountData(int userId);
    Task<int> GetUserPaymentCountry(int userId);
    Task UpdateUserMailingCountry(int userId, int countryId);
}
