﻿namespace WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.ItalyBank;
public class ItalyBankDtoValidator : AbstractValidator<ItalyBankDto>
{
    public ItalyBankDtoValidator()
    {
        RuleFor(x => x.AccountHolderName)
            .NotEmpty().WithMessage("AccountHoldeNameRequired")
            .Matches(@"^[A-Za-z ]*$").WithMessage("BankNote");

        RuleFor(x => x.BanKEmail)
            .NotEmpty().WithMessage("emailFieldIsRequired")
            .Matches(@"^([a-zA-Z0-9_\-\.]+)@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.)|(([a-zA-Z0-9\-]+\.)+))([a-zA-Z]{2,4}|[0-9]{1,3})(\]?)$");

        RuleFor(x => x.IBAN)
            .NotEmpty().WithMessage("IBANRequired")
            .MinimumLength(5).WithMessage("IBANMinlength")
            .MaximumLength(34).WithMessage("IBANMaxlength")
            .Matches("^[A-Za-z0-9]*$").WithMessage("BankNote");

        RuleFor(x => x.AccountHolderResidentialAddress)
            .NotEmpty().WithMessage("AccountHolderResidentialAddressRequired")
            .Matches("^[A-Za-z0-9\\s]*$").WithMessage("BankNote");

        RuleFor(x => x.PostCode)
            .NotEmpty().WithMessage("PostCodeRequired")
            .MaximumLength(10).WithMessage("PostCodeMaxLength")
            .Matches("^[A-Za-z0-9]*$").WithMessage("BankNote");
    }
}
