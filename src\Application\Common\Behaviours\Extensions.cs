﻿using System;
using System.Collections.Generic;
using System.Globalization;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace WaffarxWebAPIs.Application.Common.Behaviours;
public static class Extensions
{
    public static string ToSimplifiedString(this decimal value)
    {
        return value.ToString("G29", CultureInfo.InvariantCulture); 
    }
    public static decimal ToSimplifiedDecimal(this decimal value)
    {
        return Math.Floor(value * 100) / 100; 
    }
}
