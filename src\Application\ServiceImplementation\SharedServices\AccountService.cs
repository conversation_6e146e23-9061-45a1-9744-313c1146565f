﻿using Microsoft.AspNetCore.Http;
using WaffarxWebAPIs.Application.Common.Helpers;
using WaffarxWebAPIs.Application.Common.Models;
using WaffarxWebAPIs.Application.Common.Models.HelpersModels;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.AccountDTOs;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.AccountDTOs.EmailSignupModels;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.AccountDTOs.LoginDtos;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.AccountDTOs.SignupWithMobileDto;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.AccountDTOs.SignupWithMobileModels;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.AccountDTOs.SocialLoginDtos;
using WaffarxWebAPIs.Application.Common.Models.RequestDTOs.GetUserByGuid;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Account;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Verifications;
using WaffarxWebAPIs.Application.Implementations;
using WaffarxWebAPIs.Application.Interfaces;
using WaffarxWebAPIs.Application.ServiceInterface.GeneralInterfaces;
using WaffarxWebAPIs.Application.ServiceInterface.SharedInterfaces;
using WaffarxWebAPIs.Domain.Constants;
using WaffarxWebAPIs.Domain.Entities;
using WaffarxWebAPIs.Domain.Enums;
using WaffarxWebAPIs.Domain.Interfaces.SharedInterfaces;
using WaffarxWebAPIs.Domain.Models.CashBackModels;
using WaffarxWebAPIs.Domain.Models.SharedModels;
using WaffarxWebAPIs.Domain.Models.UsersModels;
using WaffarxWebAPIs.Domain.RepositoryInterface.SharedInterfaces;

namespace WaffarxWebAPIs.Application.ServiceImplementation.SharedServices;
public class AccountService : BaseService, IAccountService
{
    private readonly IAccountRepository _accountRepository;
    private readonly ISharedRepository _sharedRepository;
    private readonly ICashBackRepository _cashbackRepository;
    private readonly ICacheService _cacheService;
    private readonly IResourceService _resourceService;
    private readonly IUserRepository _userRepository;
    private readonly IMapper _mapper;
    private readonly IWebEngageService _webEngageService;


    private readonly Random _random = new Random();
    public AccountService(IAccountRepository accountRepository
    , ISharedRepository sharedRepository, ICashBackRepository cashbackRepository
    , ICacheService cacheService, IResourceService resourceService
    , IUserRepository userRepository
    , IMapper mapper , IWebEngageService webEngageService
    , IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)
    {
        _accountRepository = accountRepository;
        _sharedRepository = sharedRepository;
        _cashbackRepository = cashbackRepository;
        _resourceService = resourceService;
        _userRepository = userRepository; 
        _mapper = mapper;
        _cacheService = cacheService;
        _webEngageService = webEngageService;
    }
    public async Task<GenericResponse<UserRegisterDto>> Signup(SignupModel model)
    {
        var registerResponse = new GenericResponse<UserRegisterDto>();   
        try
        {
            var isValidCaptcha = await RequestsHelper.ValidateCaptcha(model.accessToken);
            if (!isValidCaptcha.Success)
            {
                registerResponse.Data = new UserRegisterDto();
                registerResponse.Status = StaticValues.Error;
                registerResponse.Errors = new List<string>() { await _resourceService.GetResourceByKey("captchaRequired") };
                registerResponse.Code = "403";
                return registerResponse;
            }
            int Existed = await _accountRepository.CheckEmail(model.email);
            if (Existed == 0)
            {
                var registerRequest = _mapper.Map<UserRegisterModel>(model);
                registerRequest.registerationId = (int)RegisterationTypeEnum.Email;
                registerRequest.signupSource = "UserRegister-desktop";
                registerResponse.Data = await UserRegister(registerRequest);
                registerResponse.Status = StaticValues.Success;
                return registerResponse;
            }
            registerResponse.Data = new UserRegisterDto();
            registerResponse.Status = StaticValues.Error;
            registerResponse.Errors = new List<string>() {await _resourceService.GetResourceByKey("thisAccountAlreadyExists")};   
            return registerResponse;

        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<UserRegisterDto>> SignupWithMobile(MobileSignupDto model)
    {
        var registerResponse = new GenericResponse<UserRegisterDto>();
        registerResponse.Data = new UserRegisterDto();
        try
        {
            var isValidCaptcha = await RequestsHelper.ValidateCaptcha(model.accessToken);
            if (!isValidCaptcha.Success)
            {
                registerResponse.Data = new UserRegisterDto();
                registerResponse.Status = StaticValues.Error;
                registerResponse.Errors = new List<string>() { await _resourceService.GetResourceByKey("captchaRequired") };
                registerResponse.Code = "403";
                return registerResponse;
            }
            int checkSignUpCode = await CheckSigupVerircationCode(model.otpCode, model.mobileNumber);
            if (checkSignUpCode > 0)
            {
                string email = model.mobileNumber.Trim() + "@waffarx.com";
                int Existed = await _accountRepository.CheckEmail(email);
                if (Existed == 0)
                {
                    var registerRequest = _mapper.Map<UserRegisterModel>(model);
                    registerRequest.email = email;
                    registerRequest.registerationId = (int)RegisterationTypeEnum.MobileNumber;
                    registerRequest.signupSource = "SignupWithMobile-API";
                    registerResponse.Data = await UserRegister(registerRequest);
                    registerResponse.Status = StaticValues.Success;
                    return registerResponse;
                }
                registerResponse.Errors = new List<string>() { await _resourceService.GetResourceByKey("thisAccountAlreadyExists") };
            }
            else 
            {
                if (checkSignUpCode == 0)
                {
                    registerResponse.Errors = new List<string>() { await _resourceService.GetResourceByKey("verificationcodecorrect") };
                }
                else 
                {
                    registerResponse.Errors = new List<string>() { await _resourceService.GetResourceByKey("MobileNumberVerifiedBefore") };
                }
            }
            registerResponse.Status = StaticValues.Error;
            return registerResponse;

        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<UserRegisterDto>> SocialLogin(SocialLoginDto model)
    {
        var response = new GenericResponse<UserRegisterDto>();
        try
        {
            LoginDto loginDto = _mapper.Map<LoginDto>(model);
            response = await Login(loginDto, null);
            if (response != null && response.Status == StaticValues.Error)
            {
                UserRegisterModel registerRequest = _mapper.Map<UserRegisterModel>(model);
                registerRequest.signupSource = "SocialLogin-API";
                var registerResponse = await UserRegister(registerRequest);
                response.Data = registerResponse;
                response.Status = !string.IsNullOrEmpty(registerResponse?.guid ?? "") ? StaticValues.Success : StaticValues.Error; 
                response.Errors = !string.IsNullOrEmpty(registerResponse?.guid ?? "") ? new List<string>() : new List<string>() { await _resourceService.GetResourceByKey("anErrorHasWhileProcess") };
                return response;
            }
            return response;

        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<UserRegisterDto> UserRegister(UserRegisterModel model)
    {
        AppUser user = null;
        UserRegisterDto userRegisterResult = new UserRegisterDto();
        try
        {
            if (model != null)
            {
                user = new AppUser();
                if (!string.IsNullOrEmpty(model.password))
                {
                    PasswordModel hashedPassword = PasswordHelper.CreateNewPasswordHashed(model.password);
                    user.NewPassword = hashedPassword.HashedPassword;
                    user.HashingKey = hashedPassword.Salt;
                }
                user.Email = model.email.Trim();
                user.EmailHashed = ParseHelper.GetSHA256Hash(null, model.email);
                user.LoginsNumber = 1L;
                user.CountryId = CountryId;
                user.MailingCountry = CountryId;
                user.Guid = Guid.NewGuid().ToString();
                user.Cdate = DateTime.Now;
                user.RegestrationTypeId = model.registerationId;
                user.SignUpSource = model.sourceId;
                user.ReferralCode = await RandomString(8);
                user.IsBlocked = false;
                user.IsActive = true;
                user.Birthdate = model.signupSource;
                user.IsGuestUser = false;
                user.LanguageId = IsEnglish ? 1 : 0;
                if (!string.IsNullOrEmpty(model.fullName))
                {
                    model.fullName = model.fullName.Trim();
                    var firstSpaceIndex = model.fullName.IndexOf(" ");
                    if (firstSpaceIndex > 0)
                    {
                        var firstString = model.fullName.Substring(0, firstSpaceIndex);
                        var secondString = model.fullName.Substring(firstSpaceIndex + 1);
                        user.FirstName = firstString;
                        user.LastName = secondString;
                    }
                    else
                    {
                        user.FirstName = model.fullName;
                    }
                }
                var newUser = await _accountRepository.CreateUser(user);
                userRegisterResult = _mapper.Map<UserRegisterDto>(newUser);
                if (newUser != null)
                {
                    ReferalSettingModel CurrentCycle = await _sharedRepository.GetCurrentPaymentCycle();
                    await InsertNewUserCashBack(newUser.Id, CurrentCycle);
                    await _sharedRepository.InsertEmailSubscriber(newUser.Id, newUser.Email, newUser.FirstName);
                    await UpdateUserReferal(model.referalCode, newUser.Email, newUser.Id);
                    // Save Verification For Signup With Mobile Users
                    if (!string.IsNullOrEmpty(model.signupSource) && model.signupSource == "SignupWithMobile-API")
                    { 
                        await SaveMobileUserSignUpVerircation(model.otpCode, model.mobileNumber, newUser.Id);
                    }
                    userRegisterResult.JoinDate = ParseHelper.ConvertDateTimeToUnix(newUser.Cdate);
                    userRegisterResult.UserProgress = 40;
                    userRegisterResult.LifeTimeCashBack = 2.5m;
                    userRegisterResult.CardsCount = 0;
                    userRegisterResult.IsVerified = false;
                    await _webEngageService.SendUserDataToWebEngage(_mapper.Map<AppUsersModel>(newUser), userRegisterResult, "");
                }
                return userRegisterResult;
            }
            return new UserRegisterDto();
        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<int> InsertNewUserCashBack(int UserId, ReferalSettingModel paymentCycle)
    {

        try
        {
            decimal signupBonus = decimal.Zero;
            signupBonus = paymentCycle.WelcomeBonus ?? 0;
            InsertCashBackModel insertCashBackModel = new InsertCashBackModel()
            {
                userId = UserId ,
                signupBonus = signupBonus,
                currentCycleId = paymentCycle.Id,
                convertedEgp =  await CurrencyConvert("USD", "EGP", paymentCycle, signupBonus),
                convertedSar = await CurrencyConvert("USD", "SAR", paymentCycle, signupBonus)
            };
            return await _cashbackRepository.InsertSignupUserCashBack(insertCashBackModel);
        }
        catch (Exception)
        {
            throw ;
        }

    }
    private async Task<long> UpdateUserReferal(string referalCode, string email, int userId)
    {
        try
        {
            ReferalLog referalLog = await _sharedRepository.GetReferalLog(email); 
            if (referalLog != null)
            {
                referalLog.ToUserId = userId;
                await _sharedRepository.UpdateReferalLog(referalLog);   
            }
            else if (!string.IsNullOrEmpty(referalCode))
            {
                AppUser referedUser = await _accountRepository.GetUserByReferalCode(referalCode);
                if (referedUser != null)
                {
                    int ProgramId = await _userRepository.GetUserProgramState(referedUser.Id);
                    ReferalSettingModel CurrentCycle = await _sharedRepository.GetCurrentPaymentCycle(ProgramId);
                    ReferalLog referalLog2 = new ReferalLog();
                    referalLog2.Cdate = DateTime.Now;
                    referalLog2.FromUserId = referedUser.Id;
                    referalLog2.ReferalCode = referalCode;
                    referalLog2.ToUserBonus = CurrentCycle.BonusValueToReferal;
                    referalLog2.ToUserId = userId;
                    referalLog2.FromUserBonus = CurrentCycle.BonusValueFromReferal;
                    referalLog2.FromUserMail = referedUser.Email;
                    referalLog2.ToUserMail = email;
                    await _sharedRepository.SaveReferalLog(referalLog2);  
                }
            }
            return 1;
        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<decimal?> CurrencyConvert(string fromCurrency, string toCurrency, ReferalSettingModel paymentCycle, decimal? valueToConvert)
    {
        try
        {
            var cacheKey = "CurrencyList_" + paymentCycle.Id + "_" + fromCurrency + "_" + toCurrency;
            var CurrencyList = await _cacheService.GetAsync<List<Currency>>(cacheKey);
            if (CurrencyList == null)
            {
                CurrencyList = await _sharedRepository.GetCurrencies(fromCurrency, toCurrency, paymentCycle);

                if (CurrencyList.Any())
                {
                    await _cacheService.SetAsync(cacheKey, CurrencyList);
                }
            }

            if (CurrencyList != null && CurrencyList.Count > 0)
            {
                var Rate = CurrencyList.Min(a => a.Rate);
                valueToConvert *= Rate;
            }
            else
            {
                valueToConvert = 0;
            }
            return valueToConvert;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<string> RandomString(int length)
    {
        var RetVal = "";
        const string chars = "ABCDEFGHIJKLMNOPQRSTUVWXYZ**********";
        RetVal = new string(Enumerable.Repeat(chars, length)
          .Select(s => s[_random.Next(s.Length)]).ToArray());
        var Existed = await _accountRepository.IsExitedRefralCode(RetVal);
        if (Existed)
        {
            RetVal = await RandomString(length);
        }
        return RetVal;
    }
    public async Task<GenericResponse<SendSignupCodeResponseDto>> SetSignupVerificationCode(SendSignupCodeDto model)
    {
        var response = new GenericResponse<SendSignupCodeResponseDto>();
        var responseDto = new SendSignupCodeResponseDto();
        try
        {
            // Get Used Resources List
            List<string> ResourceKeys = new List<string>() { "VerificationCodeSentAlready", "ReviewWithCustomerServiceAA", "ReviewWithCustomerServiceSS"
                , "CheckPhoneNumberAndResend", "CodeSent2", "ExceededCount","ExceedSignupOtp", "CheckPhoneNumberAndResend","CheckPhoneNumberValidity","MobileNumberVerifiedBefore" };
            var ResourceList = await _resourceService.GetResourcesList(ResourceKeys);

            responseDto.MobileNumber = model.mobileNumber;
            responseDto.CountryCode = model.countryCode;
            responseDto.NoteWithMobile = ResourceList["VerificationCodeSentAlready"] + " " + model.mobileNumber;
            responseDto.CustomerServiceNote = ResourceList["ReviewWithCustomerServiceAA"];
            responseDto.ContactCustomerServiceNote = ResourceList["ReviewWithCustomerServiceSS"];

            int sendCodeResult = await SendSignupVerificationCode(model);
            if (sendCodeResult == 0)
            {
                response.Status = StaticValues.Error;
                response.Data = responseDto;
                response.Errors= new List<string>() { ResourceList["CheckPhoneNumberAndResend"] };
            }
            else if (sendCodeResult == 1)
            {
                response.Status = StaticValues.Success;
                response.Data = responseDto;
                response.Message = ResourceList["CodeSent2"];
            }
            else if (sendCodeResult == -1)
            {
                response.Status = StaticValues.Error;
                response.Data = responseDto;
                response.Errors = new List<string>() { ResourceList["CheckPhoneNumberValidity"] };
            }
            else if (sendCodeResult == -2)
            {
                response.Status = StaticValues.Error;
                response.Data = responseDto;
                response.Errors = new List<string>() { ResourceList["MobileNumberVerifiedBefore"] };
            }
            else if (sendCodeResult == -3)
            {
                response.Status = StaticValues.Error;
                response.Data = responseDto;
                response.Errors = new List<string>() { ResourceList["ExceededCount"], ResourceList["ExceedSignupOtp"] };
            }
            return response;    
        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<int> SendSignupVerificationCode(SendSignupCodeDto model)
    {
        int result = 0;
        try
        {
            model.mobileNumber = model.mobileNumber.Trim();
            if (!string.IsNullOrEmpty(model.mobileNumber) && model.countryCode == (int)CountryCodeEnum.KSA)
            {
                model.mobileNumber = model.mobileNumber.TrimStart('0');
            }
            if (!string.IsNullOrEmpty(model.mobileNumber) && model.mobileNumber.Length > 11 
                && model.countryCode == (int)CountryCodeEnum.EgyCode)
            {
                result = -1;
                return result;
            }
            // Check Is Verified User OR Have User In SignUpVerification
            var IsVerifiedAppUser = await _sharedRepository.IsVerifiedBefore(model.mobileNumber);
            var IsVerifiedSignUpAppUser = await _accountRepository.IsSignupBefore(model.mobileNumber);
            if (IsVerifiedAppUser  || IsVerifiedSignUpAppUser)
            {
                result = -2;
                return result;
            }

            // Get Number Of Sent Codes
            var SignupVerificationsData = await _accountRepository.GetSignupVerificationsWithMobile(model.mobileNumber);
            var SentSuccessCount = SignupVerificationsData.Where(x => x.MsgStatus == true && x.MsgId != null).Count();
            if (SentSuccessCount >= 3)
            {
                result = -3;
                return result;
            }
            //var ApprovedFromArpu = SignupVerificationsData.Where(x => x.MsgStatus == true && x.SenderId == 1).ToList();
            //var ApprovedFromAms = SignupVerificationsData.Where(x => x.MsgId != null && x.SenderId == 2).ToList();
            if (SentSuccessCount < 3)
            {
                string code = await RandomSignUpCode(6);
                if (model.countryCode == (int)CountryCodeEnum.EgyCode || model.countryCode == (int)CountryCodeEnum.KSA || model.countryCode == (int)CountryCodeEnum.UAE)
                {
                    bool SendResult = await SendSignupVerificationCodeArpu(model.mobileNumber, code, model.countryCode);
                    if (SendResult == true)
                        result = 1;
                }
                else
                {
                    bool SendResult = await SendSignupVerificationCodeAmazon(model.mobileNumber, code, model.countryCode);
                    if (SendResult == true)
                        result = 1;
                }
            }
            else
            {
                result = -3;
            }
            return result;
        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<string> RandomSignUpCode(int length)
    {
        var RetVal = "";
        const string chars = "**********";
        RetVal = new string(Enumerable.Repeat(chars, length)
          .Select(s => s[_random.Next(s.Length)]).ToArray());
        var Existed = await _accountRepository.IsExitedSignupCode(RetVal);
        if (Existed)
        {
            RetVal = await RandomSignUpCode(length);
        }
        return RetVal;
    }
    private async Task<bool> SendSignupVerificationCodeAmazon(string mobileNo, string code, int countryCode)
    {
        bool RetVal = false;
        try
        {
            string TextMsg = "Your waffarx verification code is : " + code + Environment.NewLine + "Auf*NEH2Lkx";
            var MsgID = await RequestsHelper.SendSmsAmazon(countryCode.ToString(), mobileNo, TextMsg);
            if (!string.IsNullOrEmpty(MsgID))
            {
                AppUsersSignupVerification MobileUsersVerification = new AppUsersSignupVerification
                {
                    UserId = null,
                    MobileNumber = mobileNo,
                    SenderId = 2,
                    Code = code,
                    CountryCode = countryCode,
                    MsgId = MsgID,
                    Cdate = DateTime.Now,
                    MsgStatus = true,
                    PhoneNumberHashed = ParseHelper.GetSHA256Hash(null, mobileNo)
                };
                int Id = await _accountRepository.SaveSignupVerification(MobileUsersVerification);
                RetVal = Id > 0 ? true : false; 
            }
            return RetVal;

        }
        catch (Exception )
        {
            throw;
        }
    }
    private async Task<bool> SendSignupVerificationCodeArpu(string mobileNo, string code, int countryCode)
    {
        bool RetVal = false;
        try
        {
            string TextMsg = "Your waffarx verification code is : " + code + Environment.NewLine + "Auf*NEH2Lkx";
            ArpuResponse arpuResponse = await RequestsHelper.SendSmsArpuPlus(countryCode, mobileNo.Trim(), TextMsg);
            if (arpuResponse != null)
            {
                AppUsersSignupVerification MobileUsersVerification = new AppUsersSignupVerification
                {
                    UserId = null,
                    MobileNumber = mobileNo,
                    SenderId = 1,
                    Code = code,
                    CountryCode = countryCode,
                    MsgId = arpuResponse.message_id.ToString(),
                    Cdate = DateTime.Now,
                    MsgStatus = arpuResponse.status,
                    PhoneNumberHashed = ParseHelper.GetSHA256Hash(null, mobileNo)
                };
                int Id = await _accountRepository.SaveSignupVerification(MobileUsersVerification);
                RetVal = (Id > 0 && arpuResponse.status == true) ? true : false;
            }
            return RetVal;

        }
        catch (Exception)
        {
            throw;
        }
    }
    private async Task<int> CheckSigupVerircationCode(string code, string mobileNumber)
    {
        try
        {
            int result = 0;
            var IsVerifiedAppUser = await _sharedRepository.IsVerifiedBefore(mobileNumber);
            if (!IsVerifiedAppUser)
            {
                result =  await _accountRepository.UpdateSignupVerification(code, mobileNumber);
            }
            else
            {
                result = -1;
            }
            return result;  
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<int> SaveMobileUserSignUpVerircation(string Code, string mobileNumber, int UserId)
    {
        int result = 0;
        try
        {
            AppUsersSignupVerification MobileVerification = await _accountRepository.GetLastApprovedSignupVerification(mobileNumber);
            if (MobileVerification != null)
            {
                if (MobileVerification.Code.Trim() == Code.Trim())
                {
                    AppUsersVerification appUsersVerification = new AppUsersVerification
                    {
                        UserId = UserId,
                        PhoneNumber = MobileVerification.MobileNumber,
                        SenderId = MobileVerification.SenderId,
                        MsgStatus = MobileVerification.MsgStatus,
                        Code = MobileVerification.Code,
                        CountryCode = MobileVerification.CountryCode,
                        MsgId = MobileVerification.MsgId,
                        Date = MobileVerification.Cdate,
                        PhoneNumberHashed = MobileVerification.PhoneNumberHashed,
                        VerificationStatus = MobileVerification.VerificationStatus,
                    };
                    await _sharedRepository.SaveAppUserVerification(appUsersVerification);
                }
            }
        }
        catch (Exception)
        {
        }
        return result;
    }
    public async Task<GenericResponse<UserRegisterDto>> LoginByEmailOrMobile(LoginDto loginDto)
    {
        try
        {
            var isValidCaptcha = await RequestsHelper.ValidateCaptcha(loginDto.accessToken);
            if (!isValidCaptcha.Success)
            {
                var registerResponse = new GenericResponse<UserRegisterDto>();
                registerResponse.Data = new UserRegisterDto();
                registerResponse.Status = StaticValues.Error;
                registerResponse.Errors = new List<string>() { await _resourceService.GetResourceByKey("captchaRequired") };
                registerResponse.Code = "403";
                return registerResponse;
            }
            if (loginDto.loginType == 2)
            {
                loginDto.email = loginDto.mobileNumber.Trim() + "@waffarx.com";
            }
            else
            {
                loginDto.email = loginDto.email.Trim();
            }
            var hashedPassword = await GetPasswordHashed(loginDto.email, loginDto.password);
            return await Login(loginDto, hashedPassword);
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<UserRegisterDto>> Login(LoginDto loginDto, string hashedPassword = null)
    {
        var response = new GenericResponse<UserRegisterDto>();
        var userData = new UserRegisterDto();
        try
        {
            var user = await _accountRepository.GetByUserByPredicate(x => x.Email == loginDto.email && (hashedPassword == null || x.NewPassword == hashedPassword));
            if (user != null)
            {
                if (user.isActive == false)
                {
                    response.Status = StaticValues.Error;
                    response.Data = null;
                    response.Errors = new List<string>() { await _resourceService.GetResourceByKey("AccountSuspended") };
                    return response;
                }
                else if (user.isBlocked == true)
                {
                    response.Status = StaticValues.Error;
                    response.Data = null;
                    response.Errors = new List<string>() { await _resourceService.GetResourceByKey("AccountBlocked") };
                    return response;
                }
                userData = _mapper.Map<UserRegisterDto>(user);
                userData.IsVerified = await _sharedRepository.IsVerifiedUser(user.Id);
                userData.JoinDate = ParseHelper.ConvertDateTimeToUnix(user.Cdate);
                userData.UserProgress = CalculateProgress(user, userData.IsVerified);
                var UserCashBacks = await _cashbackRepository.UserAllCashbacksByCurrency(user.Id);
                userData.LifeTimeCashBack = UserCashBacks?.LifeTimeCashBsck ?? 0m;
                userData.CardsCount = await _userRepository.GetUserCardsCount(user.Id);
                userData.ProgramId = await _userRepository.GetUserProgramState(user.Id);

                var userVerification = await _sharedRepository.GetVerificationByUserId(user.Id);
                await _webEngageService.UpdateUserWebEngageData(_mapper.Map<AppUsersModel>(user), userData, userVerification != null ? userVerification.PhoneNumberHashed : "7a9d7b19-2b31-4e88-a5a8-fd9e3f018922");
                
                response.Status = StaticValues.Success;
                response.Data = userData;
            }
            else
            {
                response.Status = StaticValues.Error;
                response.Data = null;
                if (loginDto.loginType == 2)
                {
                    int Type = await _accountRepository.IsUserSignupWithMobile(loginDto.mobileNumber);
                    if (Type == 0)
                    {
                        response.Errors = new List<string>() { await _resourceService.GetResourceByKey("IncorrectUsernameOrPassword") };
                    }
                    else if (Type == -1)
                    {
                        List<string> ResourceKeys = new List<string>() { "SigninWrongMethod", "PersistsContactCS"};
                        var ResourceList = await _resourceService.GetResourcesList(ResourceKeys);
                        response.Errors = new List<string>() { ResourceList["SigninWrongMethod"], ResourceList["PersistsContactCS"] };
                    }
                }
                else
                { 
                    response.Errors = new List<string>() { await _resourceService.GetResourceByKey("IncorrectUsernameOrPassword") };
                }
            }
            return response;
        }
        catch (Exception)
        {
            throw;
        }
    }
    private int CalculateProgress(AppUsersModel user, bool isVerified)
    {
        int userProgress = 40;
        if (isVerified == true)
        {
            userProgress += 20;
        }
        if (user.gender != null)
        {
            userProgress += 20;
        }
        if (user.BDate != null)
        {
            userProgress += 20;
        }
        return userProgress;
    }
    private async Task<string> GetPasswordHashed(string email, string password)
    {
        string HashedString = "";
        try
        {
            var User = await _accountRepository.GetUserByEmail(email);    
            if (User != null)
            {
                HashedString = PasswordHelper.GetHashedString(password, User.HashingKey);
            }
            return HashedString;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<GenericResponse<UserRegisterDto>> GetUserDataByGuid(UserDataByGuidDto userDataByGuidDto)
    {
        var response = new GenericResponse<UserRegisterDto>();
        var userData = new UserRegisterDto();
        try
        {
            var userId = await _userRepository.GetUserByGuid(userDataByGuidDto.UserId);
            var user = await _accountRepository.GetByUserByPredicate(x => x.Id == userId);
            if (user != null)
            {
                if (user.isActive == false)
                {
                    response.Status = StaticValues.Error;
                    response.Data = null;
                    response.Errors = new List<string>() { await _resourceService.GetResourceByKey("AccountSuspended") };
                    return response;
                }
                else if (user.isBlocked == true)
                {
                    response.Status = StaticValues.Error;
                    response.Data = null;
                    response.Errors = new List<string>() { await _resourceService.GetResourceByKey("AccountBlocked") };
                    return response;
                }
                userData = _mapper.Map<UserRegisterDto>(user);
                userData.IsVerified = await _sharedRepository.IsVerifiedUser(user.Id);
                userData.JoinDate = ParseHelper.ConvertDateTimeToUnix(user.Cdate);
                userData.UserProgress = CalculateProgress(user, userData.IsVerified);
                var UserCashBacks = await _cashbackRepository.UserAllCashbacksByCurrency(user.Id);
                userData.LifeTimeCashBack = UserCashBacks?.LifeTimeCashBsck ?? 0m;
                userData.CardsCount = await _userRepository.GetUserCardsCount(user.Id);
                userData.ProgramId = await _userRepository.GetUserProgramState(user.Id);
                response.Status = StaticValues.Success;
                response.Data = userData;
            }
            return response;
        }
        catch (Exception)
        {
            throw;
        }
    }
}
