﻿namespace WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.SendCashoutCode;
public class SendCashoutCodeDtoValidator : AbstractValidator<SendCashoutCodeDto>
{
    public SendCashoutCodeDtoValidator()
    {
        RuleFor(x => x.UserMobileNumber)
            .NotEmpty().WithMessage("MobileNumberRequired")
            .Matches(@"^\d{9,11}$").WithMessage("CheckPhoneNumberValidity");
    }
}
