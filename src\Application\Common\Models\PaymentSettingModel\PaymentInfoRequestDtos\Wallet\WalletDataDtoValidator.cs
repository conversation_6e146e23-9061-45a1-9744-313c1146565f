﻿namespace WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.Wallet;
public class WalletDataDtoValidator : AbstractValidator<WalletDataDto>
{
    public WalletDataDtoValidator()
    {
        RuleFor(x => x.WalletID)
            .NotNull()
            .WithMessage("WalletID is required")
            .Must(id => id.HasValue && id.Value.ToString().All(char.IsDigit))
            .WithMessage("WalletID must be numeric");

        RuleFor(x => x.WalletName)
            .NotEmpty().WithMessage("AccountHoldeNameRequired")
            .Matches(@"^[A-Za-z ]*$").WithMessage("BankNote");

        RuleFor(x => x.WalletNumber)
            .NotEmpty()
            .Matches(@"^[0-9]{11,11}$").WithMessage("CheckPhoneNumberValidity");
    }
}
