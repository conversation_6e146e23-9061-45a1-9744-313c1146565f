﻿using Microsoft.EntityFrameworkCore;
using WaffarxWebAPIs.Domain.Entities;
using WaffarxWebAPIs.Domain.Models.PaymentSettingModels;
using WaffarxWebAPIs.Domain.RepositoryInterface.SharedInterfaces;
using WaffarxWebAPIs.Infrastructure.Data;

namespace WaffarxWebAPIs.Infrastructure.Repositories.SharedRepositories;
public class PaymentSettingRepository : IPaymentSettingRepository
{
    private readonly WaffarXDbContext _context;

    public PaymentSettingRepository(WaffarXDbContext context)
    {
        _context = context;
    }

    public async Task<List<BankBranchModel>> GetBankBranches(int bankId, bool isEnglish)
    {
        try
        {
            var query = await _context.BankBranches.AsNoTracking()
                                      .Where(x => x.IsActive == true && x.BankId == bankId)
                                      .Select(a => new BankBranchModel
                                      {
                                          BankId = a.BankId,
                                          BankSwift = a.BankSwift,
                                          BranchCode = a.BranchCode,
                                          BranchName = isEnglish ? a.BranchNameEn : a.BranchNameAr,
                                          BranchSwiftCode = a.BranchSwiftCode,
                                          CCHCode = a.Cchcode,
                                          Id = a.Id
                                      })
                                      .ToListAsync();
            return query;

        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<List<BankModel>> GetBanks(bool isEnglish)
    {
        try
        {
            var query = await _context.Banks.AsNoTracking()
                .Include(x => x.BankBranches)
                .Where(x => x.IsActive == true && x.BankBranches.Any(x => x.IsActive == true))
                .Select(x => new BankModel
                {
                    Id = x.Id,
                    Abbreviation = x.Abbreviation,
                    BankName = isEnglish ? x.BankNameEn : x.BankNameAr,
                    BankSwift = x.BankSwift,
                    IsActive = x.IsActive.Value
                }).ToListAsync();
            return query;
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<List<WalletModel>> GetWallets(bool isEnglish)
    {
        try
        {
            var result = await _context.Wallets.AsNoTracking()
                                               .Where(a => a.IsActive == true)
                                               .Select(x => new WalletModel
                                               {
                                                   Id = x.Id,
                                                   IsActive = x.IsActive,
                                                   WalletName = isEnglish ? x.WalletNameEn : x.WalletNameAr
                                               }).ToListAsync();
            return result;
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<List<Country>> GetCountries()
    {
        try
        {
            var RemovedCountries = new List<int>() { 97, 150, 230 };
            var result = await _context.Countries.Where(a => !RemovedCountries.Contains(a.Id)).ToListAsync();
            return result;
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<bool> AddOrUpdateUserPaymentInfo(UserPaymentInfo model)
    {
        try
        {
            if (model.Id == 0)
            {

                await _context.UserPaymentInfos.AddAsync(model);

            }
            else
            {
                _context.UserPaymentInfos.Update(model);
            }
            var result = await _context.SaveChangesAsync();
            return result > 0;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<UserPaymentInfo> GetUserPaymentInfo(int userId)
    {
        try
        {
            var result = await _context.UserPaymentInfos
                .Include(x => x.BankBranch)
                .Include(x => x.Wallet)
                .FirstOrDefaultAsync(x => x.UserId == userId);
            return result;
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<BankModel> GetBank(int bankId, bool isEnglish)
    {
        try
        {
            var bankToGet = await _context.Banks.FirstOrDefaultAsync(x => x.Id == bankId);
            if (bankToGet != null)
            {
                return new BankModel
                {
                    Abbreviation = bankToGet.Abbreviation,
                    BankName = isEnglish ? bankToGet.BankNameEn : bankToGet.BankNameAr,
                    BankSwift = bankToGet.BankSwift,
                };
            }
            return new BankModel();

        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task<BankBranchModel> GetBankBranch(int branchId, bool isEnglish)
    {
        try
        {
            var bankBranchToGet = await _context.BankBranches.FirstOrDefaultAsync(x => x.Id == branchId);
            if (bankBranchToGet != null)
            {
                return new BankBranchModel
                {
                    BankSwift = bankBranchToGet.BankSwift,
                    BranchCode = bankBranchToGet?.BranchCode,
                    BranchName = isEnglish ? bankBranchToGet.BranchNameEn : bankBranchToGet.BranchNameAr,
                    BranchSwiftCode = bankBranchToGet?.BranchSwiftCode,
                    CCHCode = bankBranchToGet?.BranchCode,

                };
            }
            return new BankBranchModel();
        }
        catch (Exception)
        {
            throw;
        }
    }
}
