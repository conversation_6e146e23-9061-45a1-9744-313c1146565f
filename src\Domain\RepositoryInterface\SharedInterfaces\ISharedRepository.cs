﻿using WaffarxWebAPIs.Domain.Models.GenericModels;
using WaffarxWebAPIs.Domain.Models.SharedModels;

namespace WaffarxWebAPIs.Domain.RepositoryInterface.SharedInterfaces;
public interface ISharedRepository
{
    Task<ReferalSettingModel> GetCurrentPaymentCycle(int ProgramId = 0);
    Task<ReferalSettingModel> GetNextPaymentCycle(int Id, int ProgramId = 0);
    Task<List<Currency>> GetCurrencies(string fromCurrency, string toCurrency, ReferalSettingModel paymentCycle);
    Task<int> InsertEmailSubscriber(int userId, string email, string firstName);
    Task<ReferalLog> GetReferalLog(string email);
    Task<long> SaveReferalLog(ReferalLog referalLog);
    Task<long> UpdateReferalLog(ReferalLog referalLog);
    Task<bool> IsVerifiedBefore(string mobileNumber);
    Task<bool> IsVerifiedUser(int userId);
    Task<ReferalSetting> GetReferalSettingWithPaymentCycle(int paymentCycleId);
    Task<List<CashOutStatus>> GetAllCashOutStatus();
    Task<string> GetUserVerifiedMobileNumber(int userId);
    Task<StaticPageModel> StaticPageById(int Id, bool isEnglish);
    Task<AppUsersVerification> GetVerificationByUserId(int userId);
    Task<int> SaveAppUserVerification(AppUsersVerification entity);
    Task<string> GetEmailBody(int id);
    Task<string> GetSocialNetwork(int id);
    Task<bool> IsExitedVerificationCode(string verificationCode);
    Task<List<AppUsersVerification>> GetVerificationsByMobile(string mobileNumber);
    Task<List<AppUsersVerification>> GetVerificationsByUserId(int userId);
    Task<AppUsersVerificationDeletedLog> GetDeletedLogByMobile(string mobileNumber);
    Task<int> SaveVerification(AppUsersVerification entity);
    Task<AppUsersVerification> GetLastVerificationByUserId(int userId);
    Task<int> UpdateAppUsersVerification(AppUsersVerification entity);
    Task<int> InsertUserTicket(InsertTicketModel model);
    Task<int> InsertCashbackUserTicket(InsertCashbackTicketModel model);
    Task<long> AddCashoutCountry(AppUserCashOutCountry entity);
    Task<long> UpdateCashoutCountry(AppUserCashOutCountry entity);
    Task<bool> IsExitedForgetPasswordCode(string passCode);
    Task<List<MobileUsersForgetPassword>> GetForgetPasswordList(string mobileNumber, int count);
    Task<bool> IsExitedForgetCode(string forgetCode);
    Task<AppUsersVerification> GetLastVerificationByMobile(string mobileNumber);
    Task<int> SaveMobileForgetPassword(MobileUsersForgetPassword entity);
    Task<ReferalSettingModel> GetCurrentCycleForReferal(int ProgramId = 0);
    Task<List<PaymentMethodModel>> GetPaymentMethodsById(List<int> ids, int countryId, bool isEnglish);
    Task<MobileUsersForgetPassword> GetMobileForgetPasswordByMobile(string mobileNumber);
    Task<string> GetUserPaymentMethodName(int userId);
}
