﻿using Microsoft.EntityFrameworkCore;
using WaffarxWebAPIs.Domain.Entities;
using WaffarxWebAPIs.Domain.Enums;
using WaffarxWebAPIs.Domain.Interfaces.SharedInterfaces;
using WaffarxWebAPIs.Domain.Models.CashoutModels;
using WaffarxWebAPIs.Domain.Models.ReferalModels;
using WaffarxWebAPIs.Domain.Models.UserModel;
using WaffarxWebAPIs.Domain.Models.UsersModels;
using WaffarxWebAPIs.Infrastructure.Data;

namespace WaffarxWebAPIs.Infrastructure.Repositories.SharedRepositories;
public class UserRepository : IUserRepository
{
    private readonly WaffarXDbContext _context;
    public UserRepository(WaffarXDbContext context)
    {
        _context = context;
    }
    public async Task<int> GetUserByGuid(string Guid)
    {
        return await _context.AppUsers.Where(x => x.Guid == Guid).Select(x => x.Id).FirstOrDefaultAsync();
    }
    public async Task<AppUsersVerification> GetVerificationByUserId(int userId)
    {
        return await _context.AppUsersVerifications.OrderByDescending(i => i.Id).FirstOrDefaultAsync(au => au.UserId == userId);
    }
    public async Task<int> GetUserProgramState(int userId)
    {
        try
        {
            return await _context.AppUsersBanks.AsNoTracking()
                .Where(up => up.StartDate <= DateTime.Now
                    && up.EndDate >= DateTime.Now
                    && up.UserId == userId)
                .OrderByDescending(x => x.Id)
                .Select(x => x.BankId)
                .FirstOrDefaultAsync();
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<List<CashOutModel>> GetUserCashouts(int userId, bool isEnglish)
    {
        try
        {
            List<int> wantedstatuses = new List<int>() { (int)CashoutStatusEnum.Pending, (int)CashoutStatusEnum.Revised, (int)CashoutStatusEnum.Sent, (int)CashoutStatusEnum.Closed, (int)CashoutStatusEnum.Failed };

            var userCashOuts = await _context.CashOuts.AsNoTracking().AsSplitQuery()
                                 .Where(x => x.UserId == userId)
                                 .Include(c => c.Paymentmethod)
                                 .Include(c => c.ReferalSetting)
                                 .Include(c => c.CashOutStatus)
                                 .Where(c => wantedstatuses.Contains(c.StatusId) && c.PaymentmethodId != (int)PaymentMethodEnum.Valu)
                                 .Select(cs => new CashOutModel
                                 {
                                     Id = cs.Id,
                                     UserId = cs.UserId,
                                     CDate = cs.Cdate,
                                     PaymentCycleId = cs.PaymentCycleId,
                                     PaymentMethodId = cs.PaymentmethodId.Value,
                                     PaymentMethodName = isEnglish ? cs.Paymentmethod.Name : cs.Paymentmethod.NameAr,
                                     Recipt = cs.Recipt,
                                     StatusId = cs.StatusId,
                                     Total = cs.TotalPaidUsd ?? 0m ,
                                     TotalUSD = cs.TotalPaidUsd ?? 0m,
                                     TotalEGP = cs.TotalPaidEgp ?? 0m,
                                     TotalSAR = cs.TotalPaidSar ?? 0m,
                                     Currency = cs.Currency,
                                     CurrencySymbol = cs.Currency,
                                     FailedText = string.Empty,
                                     PaymentCyclePayDate = cs.ReferalSetting.PaymentDate,
                                     StatusName = isEnglish ? cs.CashOutStatus.StatusNameEn : cs.CashOutStatus.StatusNameAr,
                                     CashoutCountryID = cs.CountryId.Value

                                 })
                                 .OrderByDescending(x => x.Id)
                                 .ToListAsync();

            return userCashOuts;


        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<UserPaymentInfoModel> GetUserPaymentInfo(int userid, bool isEnglish)
    {
        try
        {
            var result = await _context.UserPaymentInfos
                              .Include(pi => pi.PaymentMethod)
                              .Include(pi => pi.Wallet)
                              .Include(pi => pi.Bank)
                              .Include(pi => pi.BankBranch)
                              .Where(a => a.UserId == userid)
                              .Select(pi => new UserPaymentInfoModel
                              {
                                  AccountHolderName = pi.AccountHolderName ?? "",
                                  AccountNumber = pi.AccountNumber ?? "",
                                  BankAddress = pi.BankAddress ?? "",
                                  BankBranchName = isEnglish == true ? (pi.BankBranch != null ? pi.BankBranch.BranchNameEn : "") : (pi.BankBranch != null ? pi.BankBranch.BranchNameAr : ""),
                                  BankName = isEnglish == true ? (pi.Bank != null ? pi.Bank.BankNameEn : null) : (pi.Bank != null ? pi.Bank.BankNameAr : null),
                                  BillingAddress = pi.BillingAddress ?? "",
                                  CDate = pi.Cdate,
                                  CountryID = pi.CountryId,
                                  IBAN = pi.Iban ?? "",
                                  PaymentMethodId = pi.PaymentMethodId,
                                  SortCode = pi.SortCode ?? "",
                                  SwiftCode = pi.SwiftCode ?? "",
                                  FawryMobile = pi.FawryMobile ?? "",
                                  FawryName = pi.FawryName ?? "",
                                  // Additional detailed info
                                  BankID = pi.BankId,
                                  BranchID = pi.BranchId,
                                  OrangeMoneyNo = pi.OrangeMoneyNo,
                                  VodafoneCashNo = pi.VodafoneCashNo,
                                  DOB = pi.Dob,
                                  IsActive = pi.IsActive,
                                  FirstName = pi.FirstName,
                                  MiddleName = pi.MiddleName,
                                  LastName = pi.LastName,
                                  WalletID = pi.WalletId,
                                  WalletNo = pi.WalletNo,
                                  PostalCode = pi.PostalCode,
                                  BankEmail = pi.BankEmail,

                                  // Payment method info
                                  PaymentMethodName = isEnglish ? pi.PaymentMethod.Name : pi.PaymentMethod.NameAr,
                                  CreditCardNumber = pi.CreditCardNumber ?? "",
                                  CreditCardMaskedNumber = pi.Ccmasked,
                                  AGCODStoreId = pi.AgcodstoreId,
                                  CibAccountNumber = pi.CibaccountNumber,
                                  SelectedCreditCard = 0,
                                  WalletName = isEnglish ? (pi.Wallet != null ? pi.Wallet.WalletNameEn : null) : (pi.Wallet != null ? pi.Wallet.WalletNameAr : null),
                                  Ccmasked = pi.Ccmasked,
                              }).FirstOrDefaultAsync();

            if (result != null)
            {
                //result.CibAccountMsg = new List<string>() { "" };
                // result.CanChangeMethod = true;
                if (result.PaymentMethodId == (int)PaymentMethodEnum.CashU)
                {
                    result.PageTypeId = "1007";
                }
                if ((result.CountryID == (int)CountryEnum.Egypt || result.CountryID == (int)CountryEnum.Morocco || result.CountryID == (int)CountryEnum.SaudiArabia || result.CountryID == (int)CountryEnum.Italy) && result.PaymentMethodId != (int)PaymentMethodEnum.CashU)
                {
                    result.PageTypeId = result.CountryID + "_" + result.PaymentMethodId;

                }
                else if (result.CountryID != (int)CountryEnum.Egypt && result.CountryID != (int)CountryEnum.Morocco && result.CountryID != (int)CountryEnum.SaudiArabia && result.CountryID != (int)CountryEnum.Italy && result.PaymentMethodId != (int)PaymentMethodEnum.CashU)
                {
                    result.PageTypeId = "10022";
                }
                if (result.PaymentMethodId == (int)PaymentMethodEnum.CIBCard)
                {
                    result.VerifiedMobileNumber = await GetUserMobileNumber(result.UserId);
                    result.CreditCardNumber = result.CreditCardMaskedNumber;


                    int cardId = await _context.InStoreOffersCardTokens.Where(x => x.UserId == result.UserId && x.MaskedPan == result.CreditCardMaskedNumber
                     && x.CreatedAt.HasValue && (x.IsDisabled.HasValue && !x.IsDisabled.Value)).Select(x => x.RowId).FirstOrDefaultAsync();
                    result.SelectedCreditCard = cardId;
                }
                if (result.PaymentMethodId == (int)PaymentMethodEnum.CIBBankTransfer)
                {
                    result.CreditCardNumber = result.CreditCardMaskedNumber;
                    result.CibAccountNumber = (!string.IsNullOrEmpty(result.CibAccountNumber)) ? result.CibAccountNumber : string.Empty;

                }
                if (result.PaymentMethodId == (int)PaymentMethodEnum.AmazonGiftCard)
                {
                    result.PageTypeId = "1016";
                }
                if (result.PaymentMethodId == (int)PaymentMethodEnum.NeqatyProgram)
                {
                    result.PageTypeId = "1019";
                }
                result.NeqatyMobileNumber = !string.IsNullOrEmpty(result.OrangeMoneyNo) ? result.OrangeMoneyNo : "";
                if (result.NeqatyMobileNumber.Contains("+966"))
                {
                    result.NeqatyMobileNumber = result.OrangeMoneyNo.Replace("+966", "");
                }

            }

            return result;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<string> GetUserMobileNumber(int? UserId = null)
    {
        try
        {
            string mobile = "";
            if (UserId != null && UserId > 0)
            {
                var appUsersVerification = await _context.AppUsersVerifications.Where(x => x.UserId == UserId).OrderByDescending(i => i.Id).FirstOrDefaultAsync();

                mobile = appUsersVerification?.PhoneNumber;
            }
            return mobile;
        }
        catch (Exception)
        {

            throw;
        }
    }
    public async Task<bool> GetUserProgramStateByBankId(int userId, int bankId)
    {
        try
        {
            var userBank = await _context.AppUsersBanks.Where(up => up.StartDate <= DateTime.Now
                        && up.EndDate >= DateTime.Now
                        && up.BankId == bankId && up.UserId == userId).FirstOrDefaultAsync();

            if (userBank != null)
            {
                return true;
            }
            return false;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<AppUserModel> GetUserById(int userId)
    {
        if (userId == 0)
        {
            return null;
        }
        AppUserModel result = null;
        try
        {
            result = await _context.AppUsers.Where(x => x.Id == userId).Select(us => new AppUserModel
            {
                FirstName = us.FirstName,
                LastName = us.LastName,
                DisplayName = us.FirstName + " " + us.LastName,
                Email = us.Email,
                Gender = us.Gender,
                BDate = us.Bdate,
                Mobile = us.Mobile,
                Guid =  us.Guid,
            }).FirstOrDefaultAsync();

            return result;
        }
        catch (Exception)
        {
            return result;
        }
    }
    public async Task<AppUserModel> UpdateUserMainInfo(int userId, UpdateUserModel userToUpdate)
    {
        try
        {
            var userToGet = await _context.AppUsers.Where(x => x.Id == userId).FirstOrDefaultAsync();
            if (userToGet == null)
            {
                return new AppUserModel();
            }
            if (!string.IsNullOrEmpty(userToUpdate.FullName))
            {
                userToUpdate.FullName = userToUpdate.FullName.Trim();
                var firstSpaceIndex = userToUpdate.FullName.IndexOf(" ");
                if (firstSpaceIndex > 0)
                {
                    var firstString = userToUpdate.FullName.Substring(0, firstSpaceIndex);
                    var secondString = userToUpdate.FullName.Substring(firstSpaceIndex + 1);
                    userToGet.FirstName = firstString;
                    userToGet.LastName = secondString;
                }
                else
                {
                    userToGet.FirstName = userToUpdate.FullName;
                    userToGet.LastName = "";
                }

            }
            if (userToUpdate.Gender != null)
            {
                userToGet.Gender = userToUpdate.Gender;
            }
            if (userToUpdate.BirthDate != null)
            {
                userToGet.Bdate = DateOnly.FromDateTime(userToUpdate.BirthDate.Value);
            }
            userToGet.IsEdited = true;
            _context.Set<AppUser>().Update(userToGet);
            var result = await _context.SaveChangesAsync();
            if (result > 0)
            {
                return new AppUserModel
                {
                    Id = userToGet.Id,
                    FirstName = userToGet.FirstName,
                    LastName = userToGet.LastName,
                    DisplayName = userToGet.FirstName + " " + userToGet.LastName,
                    Email = userToGet.Email,
                    BDate = userToGet.Bdate,
                    Gender = userToGet.Gender,
                };
            }
            return new AppUserModel();

        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<int> UpdateUserFavoriteList(int userId, Guid storeGuid)
    {
        try
        {
            var storeToGet = await _context.Vadvertisers.FirstOrDefaultAsync(a => a.StoreGuid == storeGuid);
            if (storeToGet == null)
            {
                return -1;
            }
            var favoriteListToGet = await _context.FavoriteLists
                .FirstOrDefaultAsync(a => a.StoreId == storeToGet.Id && a.UserId == userId);
            if (favoriteListToGet != null)
            {
                _context.FavoriteLists.Remove(favoriteListToGet);
            }
            else
            {
                var favoriteList = new FavoriteList
                {
                    StoreId = storeToGet.Id,
                    UserId = userId
                };
                await _context.FavoriteLists.AddAsync(favoriteList);
            }
            return await _context.SaveChangesAsync();
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<int> UpdateUserVerification(int userId)
    {
        try
        {
            var user = await _context.AppUsers.FirstOrDefaultAsync(a => a.Id == userId);
            if (user != null)
            {
                user.IsVerified = true;
                await _context.SaveChangesAsync();
                return user.Id; 
            }
            return 0;   
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<int> GetUserCardsCount(int userId)
    {
        return await _context.InStoreOffersCardTokens.Where(x => x.UserId == userId
                && x.CreatedAt.HasValue && x.IsActive.HasValue && x.IsActive.Value
                && (x.IsDisabled.HasValue && !x.IsDisabled.Value)).CountAsync();
    }
    public async Task<UserEmailModel> CheckUserEmailForReferal(int userId, string email)
    {
        try
        {
            var user = await _context.AppUsers.AsNoTracking().Where(x => x.Id == userId).FirstOrDefaultAsync();
            var emailFoundCount = await _context.AppUsers.AsNoTracking().Where(x => x.Email == email).CountAsync();
            UserEmailModel model = new UserEmailModel();
            if (await _context.ReferalLogs.AsNoTracking().Where(a => a.ToUserMail == email).CountAsync() > 0)
            {
                model.Result = -1;
            }
            else if (email == user.Email)
            {
                model.Result = -2;
            }
            else if (emailFoundCount > 0)
            {
                model.Result = -3;
            }
            model.ReferralCode = user.ReferralCode;
            model.FirstName = user.FirstName;
            model.LastName = user.LastName;
            return model;
        }
        catch(Exception)
        {
            throw;
        }

    }
    public async Task<long> AddReferalLogToUser(UserRerferalLogModel model)
    {
        try
        {
            ReferalLog referalLog = new ReferalLog();
            referalLog.Cdate = DateTime.Now;
            referalLog.FromUserId = model.UserId;
            referalLog.ReferalCode = model.ReferralCode;
            referalLog.ToUserMail = model.Email;
            referalLog.ToUserValue = model.BonusValueToReferal;
            referalLog.FromUserValue = model.BonusValueFromReferal;
            await _context.ReferalLogs.AddAsync(referalLog);
            await _context.SaveChangesAsync();
            return referalLog.Id;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<List<ReferalLogModel>> GetUserReferalLog(int userId)
    {
        return await _context.ReferalLogs.AsNoTracking().Where(x => x.FromUserId == userId).Select(rl => new ReferalLogModel 
        {
            Id = rl.Id,
            CDate = rl.Cdate,
            FromUserBonus = rl.FromUserBonus,
            FromUserId = rl.FromUserId,
            FromUserMail = rl.FromUserMail,
            FromUserValue = rl.FromUserValue,
            IsUserFromCompleted = rl.IsUserFromCompleted,
            IsUserToCompleted = rl.IsUserToCompleted,
            ReferalCode = rl.ReferalCode,
            ToUserBonus = rl.ToUserBonus,
            ToUserId = rl.ToUserId,
            ToUserMail = rl.ToUserMail,
            ToUserValue = rl.ToUserValue
        }).ToListAsync();
    }
    public async Task<UpdateEmailSubscribeResponseModel> UpdateEmailSubscribe(int userId, int? countryId = (int)CountryEnum.Global)
    {
        UpdateEmailSubscribeResponseModel response = new UpdateEmailSubscribeResponseModel();
        try
        {
            var user = await _context.AppUsers.FirstOrDefaultAsync(a => a.Id == userId);
            if (user == null)
            {
                response.Status = false;
                return response;
            }
            var emailSubscriberToGet = await _context.EmailSubscribers.FirstOrDefaultAsync(a => a.UserId == userId);
            if (emailSubscriberToGet == null)
            {
                var emailSubscriber = new EmailSubscriber
                {
                    CountryId = countryId,
                    Email = user.Email,
                    FirstName = user.FirstName,
                    UserId = userId,
                    StatusId = true
                };
                await _context.EmailSubscribers.AddAsync(emailSubscriber);
            }
            else
            {
                emailSubscriberToGet.StatusId = !emailSubscriberToGet.StatusId;

                _context.Update(emailSubscriberToGet);
            }
            await _context.SaveChangesAsync();
            response.Status = true;
            response.Subscription = emailSubscriberToGet.StatusId ?? true;
            return response;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<bool> GetUserEmailSubscribe(int userId)
    {
        try
        {
            var emailSubscribe = await _context.EmailSubscribers.FirstOrDefaultAsync(x => x.UserId == userId);
            if (emailSubscribe != null)
            {
                return emailSubscribe.StatusId.HasValue ? emailSubscribe.StatusId.Value : false;
            }
            return false;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<string> GetUserReferalCode(int userId)
    {
        return await _context.AppUsers.Where(x => x.Id == userId).Select(x => x.ReferralCode).FirstOrDefaultAsync();
    }
    public async Task<UserPaymentInfoModel> GetUserPaymentInfoByUserId(int userId)
    {
        return await _context.UserPaymentInfos.Select(x => new
        UserPaymentInfoModel
        {
            CountryID = x.CountryId,
            UserId = x.UserId
        }
        ).FirstOrDefaultAsync(x => x.UserId == userId);
    }
    public async Task<int> GetUserIdByEmail(string email)
    {
        return await _context.AppUsers.Where(x => x.Email == email).Select(x => x.Id).FirstOrDefaultAsync();
    }
    public async Task<long> AddUserForgetPassword(AppUserForgetPassword entity)
    {
        try
        {
            _context.Set<AppUserForgetPassword>().Add(entity);
            await _context.SaveChangesAsync();
            return entity.Id;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<AppUserForgetPassword> GetUserForgetPassword(long id, string code)
    {
        return await _context.AppUserForgetPasswords.Where(x => x.Id == id && x.PassCode == code).FirstOrDefaultAsync();
    }
    public async Task<MobileUsersForgetPassword> GetMobileForgetPasswordByCode(string code)
    {
        return await _context.MobileUsersForgetPasswords.Where(x => x.Code == code).FirstOrDefaultAsync();
    }
    public async Task<int> UpdateUserPassword(int userId, PasswordUpdateModel hashedPassword)
    {
        try
        {
            var user = await _context.AppUsers.FirstOrDefaultAsync(a => a.Id == userId);
            if (user != null)
            {
                user.NewPassword = hashedPassword.HashedPassword;
                user.HashingKey = hashedPassword.Salt;
                await _context.SaveChangesAsync();
                return user.Id;
            }
            return 0;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<long> UpdateUserForgetPassword(AppUserForgetPassword entity)
    {
        try
        {
            _context.Set<AppUserForgetPassword>().Update(entity);
            await _context.SaveChangesAsync();
            return entity.Id;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<List<AppUserDeleteReasonModel>> DeletionReasonsList(bool isEnglish)
    {
        List<AppUserDeleteReasonModel> Reasons = new List<AppUserDeleteReasonModel>();
        try
        {
            Reasons = await _context.AppUsersDeletedReasons.Where(d => d.IsActive == true)
                .Select(dr => new AppUserDeleteReasonModel
                {
                    Id = dr.Id,
                    ReasonText = isEnglish ? dr.ReasonText : dr.ReasonTextAr,
                }).ToListAsync();
            return Reasons;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<int> InsertDeletedUserRequest(DeleteAccountModel model)
    {
        try
        {
            AppUsersDeleted UserRequest = new AppUsersDeleted()
            {
                ReasonId = model.ReasonId,
                ReasonText = model.ReasonText,
                UserId = model.UserId,
                StatusId = false,
                Cdate = DateTime.Now,
            };

            _context.AppUsersDeleted.Add(UserRequest);
            await _context.SaveChangesAsync();
            return UserRequest.Id;

        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<int> DeletUserAccountData(int userId)
    {
        using (var tran = _context.Database.BeginTransaction())
        {
             // await using var transaction = await _context.Database.BeginTransactionAsync();
            try
            {
                var user = await _context.AppUsers.Where(au => au.Id == userId).FirstOrDefaultAsync();
                if (user != null)
                {
                    string SignUpMobile = "";
                    if (user.RegestrationTypeId == 6)
                    {
                        SignUpMobile = user.Email.Trim().ToLower().Replace("@waffarx.com", "");
                    }
                    // Update User Email And Make User InActive
                    string Email = "DeletedUser-" + user.Id.ToString() + "@waffax.com";
                    user.Email = Email;
                    user.IsActive = false;
                    user.IsBlocked = true;
                    user.Mobile = string.Empty;
                    user.FirstName = "Deleted";
                    user.LastName = "Account";
                    user.Bdate = null;
                    _context.Entry(user).State = EntityState.Modified;

                    //Update User Verification Numbers
                    var userVerification = await _context.AppUsersVerifications.Where(au => au.UserId == userId).ToListAsync();
                    if (userVerification != null && userVerification.Count > 0)
                    {
                        userVerification.ForEach(x => { x.PhoneNumber = x.Code + "-0000"; });
                    }

                    //Delete User Verification Logs
                    var userVerificationLogs = await _context.AppUsersVerificationDeletedLogs.Where(au => au.UserId == userId).ToListAsync();
                    if (userVerificationLogs != null && userVerificationLogs.Count > 0)
                    {
                        _context.AppUsersVerificationDeletedLogs.RemoveRange(userVerificationLogs);
                    }

                    // Delete User PaymentInfo
                    var paymentInfo = await _context.UserPaymentInfos.Where(x => x.UserId == user.Id).FirstOrDefaultAsync();
                    if (paymentInfo != null)
                    {
                        _context.UserPaymentInfos.Remove(paymentInfo);

                        //Delete User PaymentInfo n Logs
                        var paymentInfoLogs = await _context.UserPaymentInfoLogs.Where(au => au.UserId == user.Id).ToListAsync();
                        if (paymentInfoLogs != null && paymentInfoLogs.Count > 0)
                        {
                            _context.UserPaymentInfoLogs.RemoveRange(paymentInfoLogs);
                        }
                    }

                    //Delete User Cards Logs
                    var userCards = await _context.InStoreOffersCardTokens.Where(au => au.UserId == userId).ToListAsync();
                    if (userCards != null && userCards.Count > 0)
                    {
                        _context.InStoreOffersCardTokens.RemoveRange(userCards);
                    }

                    //Delete User Signup Verification Logs
                    var userCashoutVerifications = await _context.AppUsersVerificationCashouts.Where(au => au.UserId == userId).ToListAsync();
                    if (userCashoutVerifications != null && userCashoutVerifications.Count > 0)
                    {
                        _context.AppUsersVerificationCashouts.RemoveRange(userCashoutVerifications);
                    }
                    // Update Email Subscribtion
                    var emailSubscriber = await _context.EmailSubscribers.Where(x => x.UserId == user.Id).FirstOrDefaultAsync();
                    if (emailSubscriber != null)
                    {
                        emailSubscriber.Email = Email;
                        emailSubscriber.FirstName = "";
                        emailSubscriber.StatusId = false;
                        _context.Entry(emailSubscriber).State = EntityState.Modified;
                    }

                    // Update Email Subscribtion
                    var notificaionSettings = await _context.MobNotificationSettings.Where(x => x.UserId == userId).FirstOrDefaultAsync();
                    if (notificaionSettings != null)
                    {
                        notificaionSettings.CashoutNotification = false;
                        notificaionSettings.CashBackNotification = false;
                        notificaionSettings.PromotionalNotification = false;
                        _context.Entry(notificaionSettings).State = EntityState.Modified;
                    }

                    //Delete User Signup Verification Logs
                    var userSignupVerificationLogs = await _context.AppUsersSignupVerifications.Where(au => au.MobileNumber == SignUpMobile).ToListAsync();
                    if (userSignupVerificationLogs != null && userSignupVerificationLogs.Count > 0)
                    {
                        _context.AppUsersSignupVerifications.RemoveRange(userSignupVerificationLogs);
                    }

                    //Update User Progrms 
                    var userPrograms = await _context.AppUsersBanks.Where(au => au.UserId == userId).ToListAsync();
                    if (userPrograms != null && userPrograms.Count > 0)
                    {
                        userPrograms.ForEach(x => { x.EndDate = DateTime.Now; });
                    }

                    //Update User ReferalLogs 
                    var userReferalLogs = await _context.ReferalLogs.Where(au => au.FromUserId == userId).ToListAsync();
                    if (userReferalLogs != null && userReferalLogs.Count > 0)
                    {
                        userReferalLogs.ForEach(x => { x.FromUserMail = Email; });
                    }

                    // Update CashBacks And Cashouts
                    var pendingCashout = await _context.CashOuts.Where(co => co.UserId == userId && (co.StatusId == 1 || co.StatusId == 2)).ToListAsync();
                    if (pendingCashout != null && pendingCashout.Count > 0)
                    {
                        foreach (var item in pendingCashout)
                        {
                            item.StatusId = 5;
                            var cashoutDetails = await _context.CashOutDetails.Where(cod => cod.CashOutId == item.Id).ToListAsync();
                            if (cashoutDetails.Count > 0)
                            {
                                cashoutDetails.ForEach(cod => { cod.StatusId = 5; });

                                var cashBackIds = cashoutDetails.Select(cod => cod.CashBackId).ToList();
                                var cashouCashBaccks = await _context.CashBacks.Where(cb => cashBackIds.Contains(cb.Id)).ToListAsync();
                                cashouCashBaccks.ForEach(cod => { cod.IsResolved = false; });
                            }
                        }
                    }

                    var userCashBacks = await _context.CashBacks.Where(cb => cb.UserId == userId && cb.IsResolved == false && cb.StatusId != 4).ToListAsync();
                    if (userCashBacks != null && userCashBacks.Count > 0)
                    {
                        var onlineNotResolved = userCashBacks.Where(cb => cb.CashBackTypeId == 1).Select(x => x.ExitClickId).ToList();
                        var inStroreNotResolved = userCashBacks.Where(cb => cb.CashBackTypeId == 7).Select(x => x.ExitClickId).ToList();
                        foreach (var item in userCashBacks)
                        {
                            if (item.CashBackTypeId == 1 || item.CashBackTypeId == 7)
                            {
                                item.WaffarXconvertedValue = item.WaffarXconvertedValue + item.UserConvertedValue;
                                item.WaffarXconvertedValueEgp = item.WaffarXconvertedValueEgp + item.UserConvertedValueEgp;
                                item.WaffarxConvertedValueSar = item.WaffarxConvertedValueSar + item.UserConvertedValueSar;
                                item.UserConvertedValue = 0m;
                                item.UserConvertedValueEgp = 0m;
                                item.UserConvertedValueSar = 0m;
                                item.NetworkCommision = 0m;
                            }
                            else
                            {
                                item.StatusId = 4;
                            }
                        }
                        var exitClicks = await _context.ExitClicks.Where(ec => onlineNotResolved.Contains(ec.Id) && ec.UserId == userId).ToListAsync();
                        if (exitClicks.Count > 0)
                        {
                            exitClicks.ForEach(ec => { ec.AdvertiserCommision = 0m; });
                        }

                        var inStoreLinks = await _context.InStoreCardLinks.Where(ec => inStroreNotResolved.Contains(ec.Id) && ec.UserId == userId).ToListAsync();
                        if (inStoreLinks.Count > 0)
                        {
                            inStoreLinks.ForEach(ec => { ec.UserPercentage = 0m; });
                           
                        }
                    }

                    await _context.SaveChangesAsync();
                    tran.Commit();
                    return 1;

                }
                return -2;
            }
            catch (Exception)
            {
                tran.Rollback();
                throw;
            }
        }
    }
    public async Task<bool> UpdateDeleteUserRequest(int userId, string requestId = "", string message = "")
    {
        try
        {
            var userRequest = await _context.AppUsersDeleted.Where(x => x.UserId == userId).FirstOrDefaultAsync();
            if (userRequest != null)
            {
                userRequest.WebEngageRequestId = requestId;
                userRequest.WebEnageResponseJson = message;
                userRequest.StatusId = true;
                _context.Entry(userRequest).State = EntityState.Modified;
                await _context.SaveChangesAsync();
                return true;
            }
            return false;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public async Task<int> GetUserPaymentCountry(int userId)
    {
        int countryId = 0;
        try
        {
            var result = await _context.UserPaymentInfos.FirstOrDefaultAsync(a => a.UserId == userId);
            if (result != null)
            {
                countryId = result.CountryId ?? 0;  
            }
            return countryId;
        }
        catch (Exception)
        {
            throw;
        }
    }

    public async Task UpdateUserMailingCountry(int userId, int countryId)
    {
        try
        {
            var userToUpdate = await _context.AppUsers.FirstOrDefaultAsync(x => x.Id == userId);
            if (userToUpdate != null) {
                userToUpdate.MailingCountry = countryId;
                _context.AppUsers.Update(userToUpdate);
                await _context.SaveChangesAsync();
            }
        }
        catch(Exception)
        {
            throw;
        }
    }
}
