﻿using WaffarxWebAPIs.Application.Common.Models;
using WaffarxWebAPIs.Application.ServiceInterface.SharedInterfaces;

namespace WaffarxWebAPIs.Application.Common.Factories;
public interface IPaymentMethodStrategy
{
    /// <summary>
    /// Updates or creates a user payment information record for a specific payment method.
    /// </summary>
    /// <param name="settingsService">The settings service to use for database operations</param>
    /// <param name="viewModel">The view model containing payment method details</param>
    /// <returns>Result code from the operation (typically a record ID or status code)</returns>
    Task<GenericResponse<bool>> UpdatePaymentInfo(IPaymentSettingService settingsService, object viewModel);

}
