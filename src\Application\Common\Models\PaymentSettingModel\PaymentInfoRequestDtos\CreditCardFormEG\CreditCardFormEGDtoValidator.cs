﻿namespace WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.CreditCardFormEG;
public class CreditCardFormEGDtoValidator : AbstractValidator<CreditCardFormEGDto>
{
    public CreditCardFormEGDtoValidator()
    {
        RuleFor(x => x.AccountHolderName)
            .NotEmpty().WithMessage("AccountHoldeNameRequired")
            .Matches(@"^[A-Za-z ]*$").WithMessage("BankNote");

        RuleFor(x => x.BankId)
                    .NotEmpty().WithMessage("BankRequired")
                    .Must(bankId => bankId.ToString().All(char.IsDigit)).WithMessage("BankNote")
                    .When(x => x.BankId != 0);

        RuleFor(x => x.BranchId)
            .NotEmpty().WithMessage("BranchRequired")
            .Must(branchId => branchId.ToString().All(char.IsDigit)).WithMessage("BankNote")
            .When(x => x.BranchId != 0);


        RuleFor(x => x.CreditCardNo)
            .NotEmpty().WithMessage("CreditCardRequired")
            .Matches("^[0-9]*$").WithMessage("CreditCardOnlyNumbers")
            .MaximumLength(16).WithMessage("CreditCardMaxlength")
            .MinimumLength(16).WithMessage("CreditCardMinlength");

        RuleFor(x => x.SWIFTCode)
            .NotEmpty().WithMessage("SWIFTCodeRequired")
            .MaximumLength(15).WithMessage("SwiftCodeMaxlength")
            .Matches("^[A-Za-z0-9]*$").WithMessage("BankNote");
    }
}
