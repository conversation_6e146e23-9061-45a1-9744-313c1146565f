﻿using Microsoft.AspNetCore.Http;
using Microsoft.Extensions.Logging;
using WaffarxWebAPIs.Application.Common.Helpers;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Account;
using WaffarxWebAPIs.Application.Common.Models.WebEngageModels;
using WaffarxWebAPIs.Application.Implementations;
using WaffarxWebAPIs.Application.ServiceInterface.GeneralInterfaces;
using WaffarxWebAPIs.Domain.Constants;
using WaffarxWebAPIs.Domain.Models.UserModel;
using WaffarxWebAPIs.Domain.Models.UsersModels;
using WaffarxWebAPIs.Domain.RepositoryInterface.SharedInterfaces;

namespace WaffarxWebAPIs.Application.ServiceImplementation.GeneralServiecs;
public class WebEngageService : BaseService, IWebEngageService
{
    private readonly ISharedRepository _sharedRepository;
    private readonly ILogger<WebEngageService> _logger;
    public WebEngageService(ISharedRepository sharedRepository
        , ILogger<WebEngageService> logger
        , IHttpContextAccessor httpContextAccessor) : base(httpContextAccessor)   
    {
        _sharedRepository = sharedRepository;
        _logger = logger;   
    } 
    public async Task<int> SendUserDataToWebEngage(AppUsersModel User, UserRegisterDto UserData, string hashedMobile)
    {
        // UnSubcribe User From WebEngage
        WEUsersModel WEUsers = await SendUserRequestData(User, UserData, hashedMobile);
        // Start a background task without awaiting 
        _ = Task.Run(async () =>
        {
            try
            {
                await RequestsHelper.SendUserToWebEngage(WEUsers);

            }
            catch (Exception ex)
            {
                // Handle or log exceptions since they won't propagate to the caller
                _logger.LogError(StaticValues.Error, ex.StackTrace);
            }
        });
        return 1;
    }
    public async Task<int> UpdateUserWebEngageData(AppUsersModel User, UserRegisterDto UserData, string hashedMobile)
    {
        // UnSubcribe User From WebEngage
        WEUsersModel WEUsers = await SendUserRequestData(User, UserData, hashedMobile);
        // Start a background task without awaiting 
        _ = Task.Run(async () =>
        {
            try
            {
                await RequestsHelper.UpdateWebEngageUser(WEUsers);

            }
            catch (Exception ex)
            {
                // Handle or log exceptions since they won't propagate to the caller
                _logger.LogError(StaticValues.Error, ex.StackTrace);
            }
        });
        return 1;
    }
    private async Task<WEUsersModel> SendUserRequestData(AppUsersModel User, UserRegisterDto UserData, string hashedMobile)
    {
        //TimeOnly timeOnly = new TimeOnly(14, 30, 0); // 2:30 PM
        //DateTime dateTime = dateOnly.ToDateTime(timeOnly);
        WEUsersModel model = new WEUsersModel()
        {
            userId = User.Id.ToString(),
            hashedEmail = User.emailHashed,
            //birthDate = "",
            //gender = User.gender != null ? (User.gender == 0 ? "male" : "female") : "",
            firstName = User.firstName,
            lastName = User.lastName,
            hashedPhone = hashedMobile,
            emailOptIn = true,
            smsOptIn = true,
            whatsappOptIn = true,
            attributes = new WE_CustomAttributes()
            {
                BankId = UserData.ProgramId == 17 ? "17" : "",
                Language = IsEnglish ? "EN" : "AR",
                MobilyUser = UserData.ProgramId == 1019 ? true : false,
                ProgramId = UserData.ProgramId == 1019 ? "1019" : "",
                IsGuestUser = false,
                CashoutMethod = await _sharedRepository.GetUserPaymentMethodName(UserId ?? 0),
                SignupDate = User.Cdate.Value.ToString("yyyy-MM-ddTHH:mm:ss")
            }
        };

        return model;
    }
    public int UpdateUserMainDataWebEngage(AppUserModel user)
    {
        // UnSubcribe User From WebEngage
        object WEUsers = UpdateUserRequestData(user);
        // Start a background task without awaiting 
        _ = Task.Run(async () =>
        {
            try
            {
                await RequestsHelper.UpdateWebEngageUser(WEUsers);

            }
            catch (Exception ex)
            {
                // Handle or log exceptions since they won't propagate to the caller
                _logger.LogError(StaticValues.Error, ex.StackTrace);
            }
        });
        return 1;
    }
    private object UpdateUserRequestData(AppUserModel user)
    {
        
        object updateData = new object();
        if ((user.Gender.HasValue && (user.Gender == 0 || user.Gender == 1)) && user.BDate.HasValue)
        {
            TimeOnly timeOnly = new TimeOnly(00, 00, 00); 
            DateTime dateTime = user.BDate.Value.ToDateTime(timeOnly);
            updateData = new WeUpdateUserDataModel()
            {
                firstName = user.FirstName,
                lastName = user.LastName,
                userId = user.Id.ToString(),
                gender = user.Gender == 0 ? "male" : "female",
                birthDate = dateTime.ToString("yyyy-MM-ddTHH:mm:ss"),   
            };
        }
        if ((user.Gender.HasValue && (user.Gender == 0 || user.Gender == 1)))
        {
            updateData = new WE_UpdateUserGenderModel()
            {
                firstName = user.FirstName,
                lastName = user.LastName,
                userId = user.Id.ToString(),
                gender = user.Gender == 0 ? "male" : "female",
            };
        }
        if (user.BDate.HasValue)
        {
            TimeOnly timeOnly = new TimeOnly(00, 00, 00);
            DateTime dateTime = user.BDate.Value.ToDateTime(timeOnly);
            updateData = new WE_UpdateUserBDateModel()
            {
                firstName = user.FirstName,
                lastName = user.LastName,
                userId = user.Id.ToString(),
                birthDate = dateTime.ToString("yyyy-MM-ddTHH:mm:ss"),
            };
        }
        return updateData;
    }
    public int UpdateUserMobileAndSubscribtionWebEngage(int userId, int SourceId, string hashedMobile = "", bool IsSubscribed = false )
    {
        // UnSubcribe User From WebEngage
        object WEUsers = UpdateMobileAndSubscribtionRequestData(userId, SourceId, hashedMobile, IsSubscribed);
        // Start a background task without awaiting 
        _ = Task.Run(async () =>
        {
            try
            {
                await RequestsHelper.UpdateWebEngageUser(WEUsers);

            }
            catch (Exception ex)
            {
                // Handle or log exceptions since they won't propagate to the caller
                _logger.LogError(StaticValues.Error, ex.StackTrace);
            }
        });
        return 1;
    }
    private object UpdateMobileAndSubscribtionRequestData(int userId, int SourceId, string hashedMobile = "", bool IsSubscribed = false)
    {

        object updateData = new object();
        if (SourceId == 1)
        {
            updateData = new WEUpdateUserMobileModel()
            {
                userId = userId.ToString(),
                hashedPhone = hashedMobile, 

            };
        }
        if (SourceId == 2)
        {
            updateData = new WEUpdateUserSubscribtionModel()
            {
                userId = userId.ToString(),
                emailOptIn = IsSubscribed,
                smsOptIn = IsSubscribed,
                whatsappOptIn = IsSubscribed,
            };
        }
        return updateData;
    }

    public async Task<WE_Users_Response> UpdateUserState(int userId)
    {
        try
        {
            WEUpdateUserSubscribtionModel WEUsers = CreateUnSubscribetData(userId);
            WE_Users_Response Response = await RequestsHelper.UpdateWebEngageUser(WEUsers);
            return Response;
        }
        catch (Exception)
        {
            throw;
        }
    }
    private WEUpdateUserSubscribtionModel CreateUnSubscribetData(int userId)
    {
        WEUpdateUserSubscribtionModel model = new WEUpdateUserSubscribtionModel()
        {
            userId = UserId.ToString(),
            emailOptIn = false,
            smsOptIn = false,
            whatsappOptIn = false,
        };
        return model;
    }


    public async Task<WEDeleteResponse> ErasureUserData(int userId)
    {
        try
        {
            WEDeleteUserRequestModel User = CreateErasureRequestData(userId);
            WEDeleteResponse Response = await RequestsHelper.ErasureUserFromWebEngage(User);
            return Response;
        }
        catch (Exception)
        {
            throw;
        }
    }
    private WEDeleteUserRequestModel CreateErasureRequestData(int UserId)
    {
        string RequestId = Guid.NewGuid().ToString();
        WEDeleteUserRequestModel model = new WEDeleteUserRequestModel()
        {
            subject_request_id = RequestId,
            subject_request_type = "erasure",
            subject_identities = new List<SubjectIdentity>() { new SubjectIdentity()
                {
                    identity_type = "cuid",
                    identity_value = UserId.ToString()
                }}
        };
        return model;
    }

    public async Task SendWebEngageEvent(WE_EventModel eventModel)
    {
        try
        {
            await RequestsHelper.SendWebEngageEvent(eventModel);
        }
        catch(Exception)
        {
            throw;
        }
    }
}
