﻿namespace WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.ItalyBank;
public class ItalyBankDto : BasePaymentMethodDto, IBankTransferDto, IItalySpecificDto
{
    public string AccountHolderName { get; set; }
    public string BanKEmail { get; set; }
    public string IBAN { get; set; }
    public string AccountHolderResidentialAddress { get; set; }
    public string PostCode { get; set; }
    public int CountryID { get; set; }

    public string BankAddress { get; set; }

    public string BankName { get; set; }

    public string BankBranchName { get; set; }

    public string AccountNo { get; set; }

    public string SWIFTCode { get; set; }
}
