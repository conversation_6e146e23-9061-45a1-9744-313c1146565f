﻿namespace WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormNoneEG;
public class BankFormNoneEGDto : BasePaymentMethodDto , IBankTransferDto
{
    public string AccountHolderName { get; set; }
    public string BankName { get; set; }
    public string AccountHolderResidentialAddress { get; set; }
    public string BankBranchName { get; set; }
    public string BankAddress { get; set; }
    public string AccountNo { get; set; }
    public string SWIFTCode { get; set; }
    public string IBAN { get; set; }
    public int CountryID { get; set; }
}
