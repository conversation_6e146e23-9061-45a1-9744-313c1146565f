﻿using System.Net;
using System.Net.Http.Headers;
using System.Security.Authentication;
using System.Text;
using Amazon.SimpleNotificationService;
using Amazon.SimpleNotificationService.Model;
using Newtonsoft.Json;
using WaffarxWebAPIs.Application.Common.Models;
using WaffarxWebAPIs.Application.Common.Models.HelpersModels;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.User;
using WaffarxWebAPIs.Application.Common.Models.WebEngageModels;
using WaffarxWebAPIs.Domain.Enums;
using static System.Runtime.InteropServices.JavaScript.JSType;

namespace WaffarxWebAPIs.Application.Common.Helpers;
public class RequestsHelper
{
    public static async Task<ArpuResponse> SendSmsArpuPlus(int countryCode, string mobNumber, string textMsg)
    {
        var RetVal = new ArpuResponse();
        string Sender = "waffarx.com";
        try
        {
            if (countryCode == 20)
                countryCode = (int)CountryCodeEnum.Egypt;
            if (countryCode == (int)CountryCodeEnum.UAE || countryCode == (int)CountryCodeEnum.KSA)
            {
                Sender = "WAFFARX";
            }
            const SslProtocols _Tls12 = (SslProtocols)0x00000C00;
            const SecurityProtocolType Tls12 = (SecurityProtocolType)_Tls12;
            ServicePointManager.SecurityProtocol = Tls12;
            TransactionArpuContent Data = new TransactionArpuContent
            {
                account_id = 381,
                text = textMsg,
                msisdn = $"{countryCode}{mobNumber}",
                sender = Sender
            };
            var json = JsonConvert.SerializeObject(Data);
            var data = new StringContent(json, Encoding.UTF8, "application/json");
            var TARGETURL = "https://api.connekio.com/sms/single";
            HttpClientHandler handler = new HttpClientHandler() { };
            HttpClient client = new HttpClient(handler);
            var byteArray = Encoding.ASCII.GetBytes("WaffarX:E&nSn1uO!qhu(-:381");
            client.DefaultRequestHeaders.Authorization = new System.Net.Http.Headers.AuthenticationHeaderValue("Basic", Convert.ToBase64String(byteArray));
            HttpResponseMessage response = await client.PostAsync(TARGETURL, data);
            HttpContent content = response.Content;
            string result = await content.ReadAsStringAsync();
            RetVal = JsonConvert.DeserializeObject<ArpuResponse>(result);
            return RetVal;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public static async Task<string> SendSmsAmazon(string CountryCode, string MobileNo, string TextMsg)
    {
        var MsgID = "";
        try
        {
            AmazonSimpleNotificationServiceClient snsClient = new AmazonSimpleNotificationServiceClient("********************", "x6VkHf6ssJQQk4vLb0P1p+VSafnevuEE3++6Mb/W", Amazon.RegionEndpoint.USEast1);
            Dictionary<string, MessageAttributeValue> messageAttributes = new Dictionary<string, MessageAttributeValue>();
            MessageAttributeValue v1 = new MessageAttributeValue();
            v1.DataType = "String";
            v1.StringValue = "senderidx";
            messageAttributes.Add("AWS.SNS.SMS.SenderID", v1);
            MessageAttributeValue v2 = new MessageAttributeValue();
            v2.DataType = "String";
            v2.StringValue = "0.50";
            messageAttributes.Add("AWS.SNS.SMS.MaxPrice", v2);
            MessageAttributeValue v3 = new MessageAttributeValue();
            v3.DataType = "String";
            // Options: Promotional, Transactional
            v3.StringValue = "Transactional";
            messageAttributes.Add("AWS.SNS.SMS.SMSType", v3);
            string MSG = TextMsg;
            string MOB = "+" + CountryCode.Trim() + MobileNo.Trim();
            MsgID = await SendSMSMessageAsync(snsClient, MSG, MOB, messageAttributes);
            return MsgID;
        }
        catch (Exception)
        {
            throw;
        }
    }
    static async Task<string> SendSMSMessageAsync(AmazonSimpleNotificationServiceClient snsClient, string message, string phoneNumber, Dictionary<string, MessageAttributeValue> messageAttributes)
    {
        var MsgID = "";
        PublishRequest publishRequest = new PublishRequest();
        publishRequest.PhoneNumber = phoneNumber;
        publishRequest.Message = message;
        publishRequest.MessageAttributes = messageAttributes;
        try
        {
            var response = await snsClient.PublishAsync(publishRequest);
            MsgID = response.MessageId;
            return MsgID;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public static async Task<string> CallGet(string CallUrl, List<KeyValuePair<string, string>> RequestHeader = null)
    {
        HttpClientHandler handler = new HttpClientHandler() { };
        HttpClient client = new HttpClient(handler);
        if (RequestHeader != null)
        {
            foreach (var item in RequestHeader)
            {
                client.DefaultRequestHeaders.TryAddWithoutValidation(item.Key, item.Value);
            }
        }
        HttpResponseMessage response = await client.GetAsync(CallUrl);
        HttpContent content = response.Content;
        string result = await content.ReadAsStringAsync();
        return result;
    }
    public static async Task<WE_Users_Response> SendUserToWebEngage(WEUsersModel WEUsers)
    {
        var RetVal = new WE_Users_Response();
        try
        {
            const SslProtocols _Tls12 = (SslProtocols)0x00000C00;
            const SecurityProtocolType Tls12 = (SecurityProtocolType)_Tls12;
            ServicePointManager.SecurityProtocol = Tls12;

            var json = JsonConvert.SerializeObject(WEUsers);
            var data = new StringContent(json, Encoding.UTF8, "application/json");
            string coreApiBaseUrl = "https://api.webengage.com/v1/accounts/76aa7a3";
            var TARGETURL = coreApiBaseUrl + "/users";

            HttpClientHandler handler = new HttpClientHandler() { };
            HttpClient client = new HttpClient(handler);

            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.DefaultRequestHeaders.Add("x-request-i", "********");
            client.DefaultRequestHeaders.Add("Authorization", "Bearer 2495af26-a558-49d1-a1cd-1c19922bfb08");

            HttpResponseMessage response = await client.PostAsync(TARGETURL, data);
            HttpContent content = response.Content;
            string result = await content.ReadAsStringAsync();
            RetVal = JsonConvert.DeserializeObject<WE_Users_Response>(result);
            return RetVal;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public static async Task<WE_Users_Response> UpdateWebEngageUser(object WEUsers)
    {
        var RetVal = new WE_Users_Response();
        try
        {
            const SslProtocols _Tls12 = (SslProtocols)0x00000C00;
            const SecurityProtocolType Tls12 = (SecurityProtocolType)_Tls12;
            ServicePointManager.SecurityProtocol = Tls12;

            var json = JsonConvert.SerializeObject(WEUsers);
            var data = new StringContent(json, Encoding.UTF8, "application/json");
            string coreApiBaseUrl = "https://api.webengage.com/v1/accounts/76aa7a3";
            var TARGETURL = coreApiBaseUrl + "/users";

            HttpClientHandler handler = new HttpClientHandler() { };
            HttpClient client = new HttpClient(handler);

            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.DefaultRequestHeaders.Add("x-request-i", "********");
            client.DefaultRequestHeaders.Add("Authorization", "Bearer 2495af26-a558-49d1-a1cd-1c19922bfb08");

            HttpResponseMessage response = await client.PutAsync(TARGETURL, data);
            HttpContent content = response.Content;
            string result = await content.ReadAsStringAsync();
            RetVal = JsonConvert.DeserializeObject<WE_Users_Response>(result);
            return RetVal;
        }
        catch (Exception)
        {
            throw;
        }
    }
    public static async Task<WEDeleteResponse> ErasureUserFromWebEngage(WEDeleteUserRequestModel WEUsers)
    {
        var RetVal = new WEGenericResponse();

        try
        {
            const SslProtocols _Tls12 = (SslProtocols)0x00000C00;
            const SecurityProtocolType Tls12 = (SecurityProtocolType)_Tls12;
            ServicePointManager.SecurityProtocol = Tls12;

            var json = JsonConvert.SerializeObject(WEUsers);
            var data = new StringContent(json, Encoding.UTF8, "application/json");
            string ApiBaseUrl = "https://api.webengage.com/v1/accounts/76aa7a3";
            var TARGETURL = ApiBaseUrl + "/opengdpr_requests";

            HttpClientHandler handler = new HttpClientHandler() { };
            HttpClient client = new HttpClient(handler);

            client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
            client.DefaultRequestHeaders.Add("x-request-i", "********");
            client.DefaultRequestHeaders.Add("Authorization", "Bearer 2495af26-a558-49d1-a1cd-1c19922bfb08");

            HttpResponseMessage response = await client.PostAsync(TARGETURL, data);
            HttpContent content = response.Content;
            string result = await content.ReadAsStringAsync();
            RetVal = JsonConvert.DeserializeObject<WEGenericResponse>(result);
            if (RetVal != null && RetVal.response != null)
            {
                return RetVal.response;
            }

            return new WEDeleteResponse();
        }
        catch (Exception)
        {
            throw;
        }
    }
    public static async Task<CaptchaResponse> ValidateCaptcha(string response)
    {
        string secret = AppSettings.WaffarXSettings.RecaptchaPrivateKey;

        using var httpClient = new HttpClient();
        string url = $"{AppSettings.WaffarXSettings.RecaptchaBaseUrl}{secret}&response={response}";

        var jsonResult = await httpClient.GetStringAsync(url);
        return JsonConvert.DeserializeObject<CaptchaResponse>(jsonResult);
    }
    public static async Task SendWebEngageEvent(WE_EventModel eventModel)
    {
        const SslProtocols _Tls12 = (SslProtocols)0x00000C00;
        const SecurityProtocolType Tls12 = (SecurityProtocolType)_Tls12;
        ServicePointManager.SecurityProtocol = Tls12;

        var json = JsonConvert.SerializeObject(eventModel);
        var data = new StringContent(json, Encoding.UTF8, "application/json");
        string coreApiBaseUrl = "https://api.webengage.com/v1/accounts/76aa7a3";
        var TARGETURL = coreApiBaseUrl + "/events";

        HttpClientHandler handler = new HttpClientHandler() { };
        HttpClient client = new HttpClient(handler);

        client.DefaultRequestHeaders.Accept.Add(new MediaTypeWithQualityHeaderValue("application/json"));
        client.DefaultRequestHeaders.Add("x-request-i", "********");
        client.DefaultRequestHeaders.Add("Authorization", "Bearer 2495af26-a558-49d1-a1cd-1c19922bfb08");
        await client.PostAsync(TARGETURL, data);

    }
}
