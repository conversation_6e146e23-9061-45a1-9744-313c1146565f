﻿using WaffarxWebAPIs.Application.Common.Models;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.AmazonGiftCardPayment;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.BankFormNoneEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.CIBBankTransfer;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.CreditCardFormEG;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.Fawry;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.ItalyBank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.KSABank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.MorocoBank;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.PaymentInfoRequestDtos.Wallet;
using WaffarxWebAPIs.Application.Common.Models.PaymentSettingModel.SendCashoutCode;
using WaffarxWebAPIs.Application.Common.Models.ResponseDTOs.Cashout;

namespace WaffarxWebAPIs.Application.ServiceInterface.SharedInterfaces;
public interface IPaymentSettingService
{
    public Task<GenericResponse<List<BanksDto>>> GetBanks();
    public Task<GenericResponse<List<BankBranchDto>>> GetBankBranches(int bankId);
    public Task<GenericResponse<List<WalletDto>>> GetWallets();
    public Task<GenericResponse<AmazonGiftCardDto>> GetAgcodStoresByCountry(int countryId);
    public Task<GenericResponse<List<PaymentMethodDto>>> GetPaymentMethods(int countryId);
    public Task<GenericResponse<CibCashoutMethodDto>> GetCibUserMethod();
    Task<GenericResponse<CashoutCountriesDto>> GetCashoutCountries();
    Task<GenericResponse<VerificationStatusDto>> SendCashoutCode(SendCashoutCodeDto model);
    Task<GenericResponse<bool>> CheckUserCashoutCode(string code, int methodId);
    Task<GenericResponse<bool>> ValidateUserMobile(string mobileNumber, string verifiedUserNumber);

    #region Update payment variants
    public Task<bool> UpdateFawry(FawryDto model);
    public Task<bool> BankFormEGUpdate(BankFormEGDto model);
    public Task<bool> UpdateWallet(WalletDataDto model);
    public Task<bool> UpdatePaymentInfo(BankFormNoneEGDto model);
    public Task<bool> UpdatePaymentInfo(MorocoBankDto model);
    public Task<bool> UpdateItalyUserPaymentInfo(ItalyBankDto model);
    public Task<bool> UpdatePaymentInfo(KSABankDto model);
    public Task<bool> CreditCardFormEGUpdate(CreditCardFormEGDto model);
    public Task<bool> AmazonGiftCardFormUpdate(AmazonGiftCardPaymentDto model);
    public Task<bool> CibBankTransferUpdate(CIBBankTransferDto model);
    #endregion

}
